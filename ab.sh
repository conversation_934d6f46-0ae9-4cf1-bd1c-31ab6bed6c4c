#!/bin/bash

# ============================================================================
# AutoBot 智能部署脚本 v1.0
# ============================================================================
# 功能：自动化部署、配置、构建和服务管理
# 作者：AutoBot Team
# 创建时间：$(date '+%Y-%m-%d')
# ============================================================================

set -euo pipefail  # 严格模式

# ============================================================================
# 全局变量和配置
# ============================================================================

# 脚本信息
SCRIPT_VERSION="1.0.0"
SCRIPT_NAME="AutoBot 智能部署脚本"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
GRAY='\033[0;37m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Unicode 图标
ICON_SUCCESS="✅"
ICON_ERROR="❌"
ICON_WARNING="⚠️"
ICON_INFO="ℹ️"
ICON_ROCKET="🚀"
ICON_GEAR="⚙️"
ICON_DATABASE="🗄️"
ICON_CLEAN="🧹"
ICON_FIRE="🔥"
ICON_PACKAGE="📦"
ICON_BUILD="🔨"
ICON_SERVER="🖥️"
ICON_CHECK="🔍"
ICON_STOP="🛑"
ICON_START="▶️"
ICON_RESTART="🔄"
ICON_LOG="📋"
ICON_HELP="❓"
ICON_EXIT="👋"
ICON_FRONTEND="🌐"
ICON_ADMIN="👤"
ICON_MANAGE="🎛️"

# 固定端口配置（不允许修改）
SERVER_PORT=3010
FRONTEND_PORT=5200
ADMIN_PORT=5202

# 日志文件
LOG_FILE="$PROJECT_ROOT/logs/deploy-$(date '+%Y%m%d-%H%M%S').log"
mkdir -p "$PROJECT_ROOT/logs"

# ============================================================================
# 工具函数库
# ============================================================================

# 日志函数
log_info() {
    local message="$1"
    echo -e "${BLUE}${ICON_INFO} ${message}${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $message" >> "$LOG_FILE"
}

log_success() {
    local message="$1"
    echo -e "${GREEN}${ICON_SUCCESS} ${message}${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $message" >> "$LOG_FILE"
}

log_warning() {
    local message="$1"
    echo -e "${YELLOW}${ICON_WARNING} ${message}${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $message" >> "$LOG_FILE"
}

log_error() {
    local message="$1"
    echo -e "${RED}${ICON_ERROR} ${message}${NC}" >&2
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $message" >> "$LOG_FILE"
}

log_step() {
    local message="$1"
    echo -e "\n${BOLD}${CYAN}${ICON_ROCKET} ${message}${NC}"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] STEP: $message" >> "$LOG_FILE"
}

# 进度条函数
show_progress() {
    local current=$1
    local total=$2
    local width=50

    # 防止除零错误
    if [[ $total -eq 0 ]]; then
        printf "\r${CYAN}[%*s] 0%% (0/0)${NC}" $width | tr ' ' '░'
        return
    fi

    local percentage=$((current * 100 / total))
    local completed=$((current * width / total))

    printf "\r${CYAN}["
    printf "%*s" $completed | tr ' ' '█'
    printf "%*s" $((width - completed)) | tr ' ' '░'
    printf "] %d%% (%d/%d)${NC}" $percentage $current $total
}

# 旋转器函数
show_spinner() {
    local pid=$1
    local message="$2"
    local spinner='⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏'
    local i=0
    
    while kill -0 $pid 2>/dev/null; do
        printf "\r${CYAN}${spinner:$i:1} ${message}${NC}"
        i=$(((i + 1) % ${#spinner}))
        sleep 0.1
    done
    printf "\r"
}

# 用户交互函数
ask_yes_no() {
    local question="$1"
    local default="${2:-y}"
    local answer
    
    while true; do
        if [[ "$default" == "y" ]]; then
            echo -ne "${YELLOW}${ICON_INFO} ${question} [Y/n]: ${NC}"
        else
            echo -ne "${YELLOW}${ICON_INFO} ${question} [y/N]: ${NC}"
        fi
        
        read -r answer
        answer=${answer:-$default}
        
        case $answer in
            [Yy]|[Yy][Ee][Ss]) return 0 ;;
            [Nn]|[Nn][Oo]) return 1 ;;
            *) echo -e "${RED}请输入 y 或 n${NC}" ;;
        esac
    done
}

ask_input() {
    local question="$1"
    local default="$2"
    local is_password="${3:-}"
    local answer
    local prompt_suffix
    local read_options=""

    # 设置提示符后缀
    if [[ -n "$default" ]]; then
        if [[ "$is_password" == "password" ]]; then
            prompt_suffix=" [默认: ****]: "
        else
            prompt_suffix=" [默认: $default]: "
        fi
    else
        prompt_suffix=": "
    fi

    # 设置读取选项
    if [[ "$is_password" == "password" ]]; then
        read_options="-s"
    fi

    # 显示提示并读取输入
    echo -ne "${YELLOW}${ICON_INFO} ${question}${prompt_suffix}${NC}"
    read $read_options -r answer

    # 如果是密码输入，需要换行
    if [[ "$is_password" == "password" ]]; then
        echo
    fi

    echo "${answer:-$default}"
}

# ============================================================================
# 系统检查模块
# ============================================================================

check_os() {
    log_step "检查操作系统环境"
    
    local os_name
    case "$(uname -s)" in
        Darwin)
            os_name="macOS"
            log_success "检测到 macOS 系统"
            ;;
        Linux)
            os_name="Linux"
            log_success "检测到 Linux 系统"
            ;;
        *)
            log_error "不支持的操作系统: $(uname -s)"
            exit 1
            ;;
    esac
    
    # 检查架构
    local arch=$(uname -m)
    log_info "系统架构: $arch"
    
    return 0
}

check_permissions() {
    log_step "检查权限"
    
    # 检查当前目录写权限
    if [[ ! -w "$PROJECT_ROOT" ]]; then
        log_error "当前目录没有写权限: $PROJECT_ROOT"
        exit 1
    fi
    
    # 检查是否为root用户（不推荐）
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到以root用户运行，建议使用普通用户"
        if ! ask_yes_no "是否继续？"; then
            exit 1
        fi
    fi
    
    log_success "权限检查通过"
    return 0
}

check_tools() {
    log_step "检查工具版本"

    local tools_status=0

    # 检查必需工具
    local required_tools=("node" "npm" "git" "curl" "mysql" "sed" "awk" "grep")
    local optional_tools=("pnpm" "pm2")

    for tool in "${required_tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local version=""
            case "$tool" in
                "node")
                    version=$(node --version)
                    # 检查 Node.js 版本是否 >= 16.0.0
                    local node_major=$(echo "$version" | sed 's/v//' | cut -d. -f1)
                    if [[ $node_major -lt 16 ]]; then
                        log_warning "Node.js 版本过低 ($version)，建议升级到 v16.0.0 或更高版本"
                        tools_status=1
                    fi
                    ;;
                "npm") version=$(npm --version) ;;
                "git") version=$(git --version | awk '{print $3}') ;;
                "curl") version=$(curl --version | head -n1 | awk '{print $2}') ;;
                "mysql") version=$(mysql --version | awk '{print $3}' | cut -d, -f1) ;;
                "sed"|"awk"|"grep") version="系统工具" ;;
            esac
            log_success "$tool: $version"
        else
            log_error "$tool 未安装"
            case "$tool" in
                "mysql")
                    log_info "请安装 MySQL 客户端工具"
                    ;;
                "node"|"npm")
                    log_info "请安装 Node.js (https://nodejs.org/)"
                    ;;
                "git")
                    log_info "请安装 Git (https://git-scm.com/)"
                    ;;
                "curl")
                    log_info "请安装 curl"
                    ;;
            esac
            tools_status=1
        fi
    done

    for tool in "${optional_tools[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            local version=$($tool --version 2>/dev/null | head -n1)
            log_success "$tool: $version"
        else
            log_info "$tool 未安装，将在后续步骤中自动安装"
        fi
    done

    return $tools_status
}

check_network() {
    log_step "检查网络连接"
    
    # 检查基本网络连接
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_success "网络连接正常"
    else
        log_warning "网络连接可能有问题"
    fi
    
    # 检查npm registry
    if curl -s https://registry.npmjs.org/ >/dev/null 2>&1; then
        log_success "npm registry 可访问"
    else
        log_warning "npm registry 访问可能有问题"
    fi
    
    return 0
}

# ============================================================================
# 依赖安装模块
# ============================================================================

install_nodejs() {
    log_step "检查 Node.js 环境"
    
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version | sed 's/v//')
        local major_version=$(echo $node_version | cut -d. -f1)
        
        if [[ $major_version -ge 18 ]]; then
            log_success "Node.js 版本符合要求: v$node_version"
            return 0
        else
            log_warning "Node.js 版本过低: v$node_version，需要 >= 18.0.0"
        fi
    else
        log_warning "Node.js 未安装"
    fi
    
    # 安装 Node.js
    log_info "开始安装 Node.js..."
    
    case "$(uname -s)" in
        Darwin)
            if command -v brew >/dev/null 2>&1; then
                brew install node
            else
                log_error "请先安装 Homebrew 或手动安装 Node.js"
                exit 1
            fi
            ;;
        Linux)
            # 使用 NodeSource 仓库
            curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
            sudo apt-get install -y nodejs
            ;;
    esac
    
    log_success "Node.js 安装完成"
}

install_pnpm() {
    log_step "检查 pnpm 包管理器"
    
    if command -v pnpm >/dev/null 2>&1; then
        local pnpm_version=$(pnpm --version)
        log_success "pnpm 已安装: v$pnpm_version"
        return 0
    fi
    
    log_info "安装 pnpm..."
    npm install -g pnpm
    log_success "pnpm 安装完成"
}

install_pm2() {
    log_step "检查 PM2 进程管理器"
    
    if command -v pm2 >/dev/null 2>&1; then
        local pm2_version=$(pm2 --version)
        log_success "PM2 已安装: v$pm2_version"
        return 0
    fi
    
    log_info "安装 PM2..."
    npm install -g pm2
    log_success "PM2 安装完成"
}

check_mysql() {
    log_step "检查 MySQL 数据库"
    
    if command -v mysql >/dev/null 2>&1; then
        local mysql_version=$(mysql --version | awk '{print $3}' | cut -d, -f1)
        log_success "MySQL 已安装: $mysql_version"
        return 0
    else
        log_warning "MySQL 未安装"
        log_info "请手动安装 MySQL 数据库："
        case "$(uname -s)" in
            Darwin)
                log_info "  brew install mysql"
                ;;
            Linux)
                log_info "  sudo apt-get install mysql-server"
                ;;
        esac
        
        if ask_yes_no "是否已安装 MySQL？"; then
            return 0
        else
            exit 1
        fi
    fi
}

# ============================================================================
# 环境配置模块
# ============================================================================

# 配置文件备份和恢复
backup_config_files() {
    log_info "备份配置文件"

    local backup_dir="$PROJECT_ROOT/.backup/$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"

    # 备份 .env 文件
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        cp "$PROJECT_ROOT/.env" "$backup_dir/.env"
        log_info "已备份 .env 文件到 $backup_dir"
    fi

    # 备份其他重要配置文件
    local config_files=("package.json" "ecosystem.config.js")
    for file in "${config_files[@]}"; do
        if [[ -f "$PROJECT_ROOT/$file" ]]; then
            cp "$PROJECT_ROOT/$file" "$backup_dir/"
            log_info "已备份 $file 到 $backup_dir"
        fi
    done

    echo "$backup_dir" > "$PROJECT_ROOT/.last_backup"
    return 0
}

restore_config_files() {
    log_info "恢复配置文件"

    if [[ ! -f "$PROJECT_ROOT/.last_backup" ]]; then
        log_warning "没有找到备份记录"
        return 1
    fi

    local backup_dir=$(cat "$PROJECT_ROOT/.last_backup")
    if [[ ! -d "$backup_dir" ]]; then
        log_error "备份目录不存在: $backup_dir"
        return 1
    fi

    # 恢复 .env 文件
    if [[ -f "$backup_dir/.env" ]]; then
        cp "$backup_dir/.env" "$PROJECT_ROOT/.env"
        log_info "已恢复 .env 文件"
    fi

    # 恢复其他配置文件
    local config_files=("package.json" "ecosystem.config.js")
    for file in "${config_files[@]}"; do
        if [[ -f "$backup_dir/$file" ]]; then
            cp "$backup_dir/$file" "$PROJECT_ROOT/"
            log_info "已恢复 $file"
        fi
    done

    return 0
}

cleanup_on_failure() {
    log_warning "检测到操作失败，执行清理"

    # 清理临时文件
    rm -f "$PROJECT_ROOT"/.env.tmp.*
    rm -f "$PROJECT_ROOT"/.backup_in_progress

    # 如果有备份，询问是否恢复
    if [[ -f "$PROJECT_ROOT/.last_backup" ]]; then
        if ask_yes_no "是否恢复到操作前的配置？"; then
            restore_config_files
        fi
    fi
}

generate_env() {
    log_step "生成环境配置文件"

    local env_file="$PROJECT_ROOT/.env"
    local env_example="$PROJECT_ROOT/.env.example"

    # 检查是否已存在 .env 文件
    if [[ -f "$env_file" ]]; then
        log_warning ".env 文件已存在"
        if ask_yes_no "是否重新生成？"; then
            cp "$env_file" "$env_file.backup.$(date +%s)"
            log_info "已备份现有 .env 文件"
        else
            log_info "跳过 .env 文件生成"
            return 0
        fi
    fi

    # 复制示例文件
    if [[ -f "$env_example" ]]; then
        cp "$env_example" "$env_file"
        log_success "已复制 .env.example 到 .env"
    else
        log_warning ".env.example 文件不存在，创建基础配置"
        create_basic_env
    fi

    log_success "环境配置文件生成完成"
}

create_basic_env() {
    local env_file="$PROJECT_ROOT/.env"
    
    cat > "$env_file" << EOF
# AutoBot 环境配置
NODE_ENV=production
PROJECT_ROOT=$PROJECT_ROOT

# 服务端口
SERVER_PORT=$SERVER_PORT
FRONTEND_PORT=$FRONTEND_PORT
ADMIN_PORT=$ADMIN_PORT

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=autobot
DB_PASSWORD=your_database_password
DB_NAME=autobot

# JWT 配置
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=24h

# 默认管理员配置
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_EMAIL=<EMAIL>

# Dify 配置
DIFY_BASE_URL=https://api.dify.ai
DIFY_API_KEY=your-dify-api-key

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs
EOF
}

configure_env_interactive() {
    local env_file="$PROJECT_ROOT/.env"
    local backup_created=false

    # 创建配置备份
    if [[ -f "$env_file" ]] && backup_config_files; then
        backup_created=true
    fi

    echo -e "${BOLD}${CYAN}============================================================================${NC}"
    echo -e "${BOLD}${YELLOW}${ICON_GEAR} AutoBot 环境配置向导${NC}"
    echo -e "${BOLD}${CYAN}============================================================================${NC}"
    echo -e "${CYAN}请根据您的实际环境填写以下配置信息。${NC}"
    echo -e "${CYAN}如果不确定，可以使用默认值，后续可以在 .env 文件中修改。${NC}"
    echo
    
    # 数据库配置
    echo -e "${BOLD}${BLUE}📊 数据库配置${NC}"
    echo -e "${GRAY}AutoBot 需要 MySQL 数据库来存储应用数据和用户信息${NC}"
    echo
    
    echo -e "${YELLOW}数据库主机地址：${NC}"
    echo -e "${GRAY}  • 本地安装：localhost 或 127.0.0.1${NC}"
    echo -e "${GRAY}  • 远程服务器：输入服务器IP地址${NC}"
    echo -e "${GRAY}  • Docker容器：容器名称或IP${NC}"
    echo -n -e "${CYAN}数据库主机 [默认: localhost]: ${NC}"
    read -r db_host_input
    local db_host="${db_host_input:-localhost}"
    
    echo
    echo -e "${YELLOW}数据库端口：${NC}"
    echo -e "${GRAY}  • MySQL默认端口：3306${NC}"
    echo -e "${GRAY}  • 如果修改过端口，请输入实际端口号${NC}"
    echo -n -e "${CYAN}数据库端口 [默认: 3306]: ${NC}"
    read -r db_port_input
    local db_port="${db_port_input:-3306}"
    
    echo
    echo -e "${YELLOW}数据库用户名：${NC}"
    echo -e "${GRAY}  • 建议创建专用用户：autobot${NC}"
    echo -e "${GRAY}  • 或使用现有用户（需要创建数据库权限）${NC}"
    echo -e "${GRAY}  • 不建议使用root用户${NC}"
    echo -n -e "${CYAN}数据库用户名 [默认: autobot]: ${NC}"
    read -r db_user_input
    local db_user="${db_user_input:-autobot}"
    
    echo
    echo -e "${YELLOW}数据库密码：${NC}"
    echo -e "${GRAY}  • 请输入上述用户的密码${NC}"
    echo -e "${GRAY}  • 如果是新用户，请先在MySQL中创建${NC}"
    echo -e "${RED}  • 注意：密码不能为空${NC}"
    local db_password=""
    while true; do
        echo -n -e "${CYAN}数据库密码: ${NC}"
        read -s -r db_password
        echo
        if [[ -n "$db_password" ]]; then
            # 验证密码不包含特殊字符导致sed问题
            if [[ "$db_password" == *"&"* ]] || [[ "$db_password" == *"<"* ]] || [[ "$db_password" == *">"* ]] || [[ "$db_password" == *"\""* ]] || [[ "$db_password" == *"'"* ]]; then
                echo -e "${RED}${ICON_ERROR} 密码包含特殊字符，可能导致配置问题，建议避免使用 & < > ' \" 等字符${NC}"
                if ask_yes_no "是否继续使用此密码？"; then
                    break
                fi
            else
                break
            fi
        else
            echo -e "${RED}${ICON_ERROR} 数据库密码不能为空，请重新输入${NC}"
        fi
    done
    
    echo
    echo -e "${YELLOW}数据库名称：${NC}"
    echo -e "${GRAY}  • 系统将自动创建此数据库${NC}"
    echo -e "${GRAY}  • 建议使用：autobot${NC}"
    echo -n -e "${CYAN}数据库名称 [默认: autobot]: ${NC}"
    read -r db_name_input
    local db_name="${db_name_input:-autobot}"
    
    echo
    echo -e "${BOLD}${GREEN}👤 管理员账户配置${NC}"
    echo -e "${GRAY}设置系统管理员账户，用于登录管理后台${NC}"
    echo
    
    echo -e "${YELLOW}管理员用户名：${NC}"
    echo -e "${GRAY}  • 用于登录管理后台的用户名${NC}"
    echo -e "${GRAY}  • 建议使用：admin 或您的用户名${NC}"
    echo -n -e "${CYAN}管理员用户名 [默认: admin]: ${NC}"
    read -r admin_username_input
    local admin_username="${admin_username_input:-admin}"
    
    echo
    echo -e "${YELLOW}管理员密码：${NC}"
    echo -e "${GRAY}  • 用于登录管理后台的密码${NC}"
    echo -e "${RED}  • 请设置强密码，建议包含字母、数字和特殊字符${NC}"
    echo -e "${GRAY}  • 最少8位字符${NC}"
    local admin_password=""
    while [[ -z "$admin_password" || ${#admin_password} -lt 8 ]]; do
        echo -n -e "${CYAN}管理员密码: ${NC}"
        read -s -r admin_password
        echo
        if [[ -z "$admin_password" ]]; then
            echo -e "${RED}${ICON_ERROR} 管理员密码不能为空${NC}"
        elif [[ ${#admin_password} -lt 8 ]]; then
            echo -e "${RED}${ICON_ERROR} 密码长度至少8位，请重新输入${NC}"
        fi
    done
    
    echo
    echo -e "${YELLOW}管理员邮箱：${NC}"
    echo -e "${GRAY}  • 用于接收系统通知和密码重置${NC}"
    echo -e "${GRAY}  • 格式：<EMAIL>${NC}"
    echo -n -e "${CYAN}管理员邮箱 [默认: <EMAIL>]: ${NC}"
    read -r admin_email_input
    local admin_email="${admin_email_input:-<EMAIL>}"
    
    echo
    echo -e "${BOLD}${CYAN}💾 正在保存配置...${NC}"

    # 使用更安全的方法更新 .env 文件，避免特殊字符问题
    # 先备份原文件
    cp "$env_file" "$env_file.bak"

    # 使用临时文件和逐行处理的方式更新配置
    local temp_file=$(mktemp)
    while IFS= read -r line; do
        case "$line" in
            DB_HOST=*)
                echo "DB_HOST=$db_host"
                ;;
            DB_PORT=*)
                echo "DB_PORT=$db_port"
                ;;
            DB_USER=*)
                echo "DB_USER=$db_user"
                ;;
            DB_PASSWORD=*)
                echo "DB_PASSWORD=$db_password"
                ;;
            DB_NAME=*)
                echo "DB_NAME=$db_name"
                ;;
            DEFAULT_ADMIN_USERNAME=*)
                echo "DEFAULT_ADMIN_USERNAME=$admin_username"
                ;;
            DEFAULT_ADMIN_PASSWORD=*)
                echo "DEFAULT_ADMIN_PASSWORD=$admin_password"
                ;;
            DEFAULT_ADMIN_EMAIL=*)
                echo "DEFAULT_ADMIN_EMAIL=$admin_email"
                ;;
            *)
                echo "$line"
                ;;
        esac
    done < "$env_file" > "$temp_file"

    # 替换原文件
    mv "$temp_file" "$env_file"
    
    rm -f "$env_file.bak"
    
    # 检查配置文件更新是否成功
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}${ICON_SUCCESS} 配置保存完成！${NC}"
        echo
        echo -e "${BOLD}${CYAN}📋 配置摘要：${NC}"
        echo -e "${GRAY}数据库：${NC}$db_user@$db_host:$db_port/$db_name"
        echo -e "${GRAY}管理员：${NC}$admin_username ($admin_email)"
        echo
        return 0
    else
        log_error "配置保存失败"
        if [[ "$backup_created" == "true" ]]; then
            log_info "尝试恢复备份配置"
            restore_config_files
        fi
        return 1
    fi
}

set_project_root() {
    log_step "设置项目根路径"
    
    # 设置环境变量
    export PROJECT_ROOT="$PROJECT_ROOT"
    export NODE_ENV="production"
    
    # 更新 .env 文件
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        if grep -q "PROJECT_ROOT=" "$PROJECT_ROOT/.env"; then
            sed -i.bak "s|PROJECT_ROOT=.*|PROJECT_ROOT=$PROJECT_ROOT|" "$PROJECT_ROOT/.env"
        else
            echo "PROJECT_ROOT=$PROJECT_ROOT" >> "$PROJECT_ROOT/.env"
        fi
        rm -f "$PROJECT_ROOT/.env.bak"
    fi
    
    log_success "项目根路径设置完成: $PROJECT_ROOT"
}

# ============================================================================
# 数据库管理模块
# ============================================================================

check_mysql_service() {
    log_step "检查 MySQL 服务状态"
    
    # 尝试连接 MySQL
    if mysql -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "MySQL 服务运行正常"
        return 0
    else
        log_warning "MySQL 服务未运行或连接失败"
        
        # 尝试启动 MySQL 服务
        case "$(uname -s)" in
            Darwin)
                if command -v brew >/dev/null 2>&1; then
                    log_info "尝试启动 MySQL 服务..."
                    brew services start mysql
                fi
                ;;
            Linux)
                log_info "尝试启动 MySQL 服务..."
                sudo systemctl start mysql || sudo service mysql start
                ;;
        esac
        
        # 再次检查
        sleep 3
        if mysql -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "MySQL 服务启动成功"
            return 0
        else
            log_error "MySQL 服务启动失败"
            return 1
        fi
    fi
}

test_db_connection() {
    log_step "测试数据库连接"

    # 加载环境变量
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        source "$PROJECT_ROOT/.env"
    fi

    local db_host="${DB_HOST:-localhost}"
    local db_port="${DB_PORT:-3306}"
    local db_user="${DB_USER:-autobot}"
    local db_password="${DB_PASSWORD}"

    if [[ -z "$db_password" ]]; then
        log_error "数据库密码未设置"
        return 1
    fi

    # 检查MySQL客户端是否可用
    if ! command -v mysql >/dev/null 2>&1; then
        log_error "MySQL客户端不可用，请安装MySQL客户端"
        return 1
    fi

    # 测试连接（使用安全的方式传递密码）
    log_info "正在测试数据库连接: $db_user@$db_host:$db_port"
    if MYSQL_PWD="$db_password" mysql -h"$db_host" -P"$db_port" -u"$db_user" -e "SELECT 1;" >/dev/null 2>&1; then
        log_success "数据库连接测试成功"
        return 0
    else
        log_error "数据库连接失败，请检查："
        log_error "  1. MySQL服务是否运行"
        log_error "  2. 数据库用户名和密码是否正确"
        log_error "  3. 数据库主机和端口是否可访问"
        return 1
    fi
}

create_database() {
    log_step "创建数据库"
    
    # 加载环境变量
    source "$PROJECT_ROOT/.env"
    
    local db_host="${DB_HOST:-localhost}"
    local db_port="${DB_PORT:-3306}"
    local db_user="${DB_USER:-autobot}"
    local db_password="${DB_PASSWORD}"
    local db_name="${DB_NAME:-autobot}"
    
    # 检查数据库是否存在
    if MYSQL_PWD="$db_password" mysql -h"$db_host" -P"$db_port" -u"$db_user" -e "USE $db_name;" >/dev/null 2>&1; then
        log_success "数据库 $db_name 已存在"
        return 0
    fi

    # 创建数据库
    log_info "创建数据库 $db_name..."
    if MYSQL_PWD="$db_password" mysql -h"$db_host" -P"$db_port" -u"$db_user" -e "CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" >/dev/null 2>&1; then
        log_success "数据库 $db_name 创建成功"
        return 0
    else
        log_error "数据库 $db_name 创建失败"
        return 1
    fi
}

init_database() {
    log_step "初始化数据库"

    # 检查 Node.js 是否可用
    if ! command -v node >/dev/null 2>&1; then
        log_error "Node.js 未安装，无法运行数据库初始化脚本"
        return 1
    fi

    # 检查数据库初始化脚本是否存在
    if [[ ! -f "$PROJECT_ROOT/scripts/init-database.js" ]]; then
        log_error "数据库初始化脚本不存在: $PROJECT_ROOT/scripts/init-database.js"
        return 1
    fi

    # 检查是否已经初始化过
    if [[ -f "$PROJECT_ROOT/.db_initialized" ]]; then
        log_info "数据库已经初始化过，跳过初始化步骤"
        if ask_yes_no "是否重新初始化数据库？"; then
            rm -f "$PROJECT_ROOT/.db_initialized"
        else
            return 0
        fi
    fi

    # 运行数据库初始化脚本
    log_info "运行数据库初始化脚本..."
    cd "$PROJECT_ROOT"

    if node scripts/init-database.js; then
        log_success "数据库初始化完成"
        # 创建初始化标记文件
        touch "$PROJECT_ROOT/.db_initialized"
        return 0
    else
        log_error "数据库初始化失败"
        return 1
    fi
}

# ============================================================================
# 构建管理模块
# ============================================================================

install_dependencies() {
    log_step "安装项目依赖"
    
    cd "$PROJECT_ROOT"
    
    # 清理缓存
    log_info "清理缓存..."
    pnpm store prune || true
    
    # 安装依赖
    log_info "安装依赖包..."
    if pnpm install; then
        log_success "依赖安装完成"
    else
        log_error "依赖安装失败"
        return 1
    fi
}

build_packages() {
    log_step "构建项目包"
    
    cd "$PROJECT_ROOT"
    
    # 构建所有包
    log_info "构建所有包..."
    if pnpm run build:pkgs; then
        log_success "包构建完成"
    else
        log_error "包构建失败"
        return 1
    fi
}

verify_build() {
    log_step "验证构建结果"
    
    local build_dirs=(
        "packages/api/dist"
        "packages/components/dist"
        "packages/core/dist"
        "packages/helpers/dist"
        "packages/theme/dist"
    )
    
    local missing_builds=()
    
    for dir in "${build_dirs[@]}"; do
        if [[ -d "$PROJECT_ROOT/$dir" ]]; then
            log_success "✓ $dir"
        else
            log_warning "✗ $dir"
            missing_builds+=("$dir")
        fi
    done
    
    if [[ ${#missing_builds[@]} -eq 0 ]]; then
        log_success "构建验证通过"
        return 0
    else
        log_warning "部分包构建缺失，但可能不影响运行"
        return 0
    fi
}

# ============================================================================
# 端口强制管理模块
# ============================================================================

force_kill_port() {
    local port=$1
    local service_name="${2:-unknown}"
    
    log_info "🔍 检查端口 $port 占用情况..."
    
    # 获取占用进程
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [[ -n "$pids" ]]; then
        log_warning "⚠️  端口 $port 被占用，进程 PID: $pids"
        
        # 获取进程详情并终止
        for pid in $pids; do
            local cmd=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            local full_cmd=$(ps -p $pid -o args= 2>/dev/null || echo "unknown")
            
            log_info "📋 进程信息: PID=$pid, 命令=$cmd"
            log_info "📋 完整命令: $full_cmd"
            
            # 安全检查
            if is_safe_to_kill "$pid" "$cmd"; then
                # 优雅终止
                log_info "🛑 尝试优雅终止进程 $pid..."
                kill -TERM $pid 2>/dev/null || true
                sleep 2
                
                # 检查是否还存在
                if kill -0 $pid 2>/dev/null; then
                    log_warning "💥 强制终止进程 $pid..."
                    kill -KILL $pid 2>/dev/null || true
                    sleep 1
                fi
                
                # 最终检查
                if kill -0 $pid 2>/dev/null; then
                    log_error "❌ 进程 $pid 终止失败"
                else
                    log_success "✅ 进程 $pid 已终止"
                fi
            else
                log_warning "⚠️  跳过系统关键进程 $pid ($cmd)"
            fi
        done
        
        # 验证端口释放
        verify_port_free $port
    else
        log_success "✅ 端口 $port 空闲"
    fi
}

is_safe_to_kill() {
    local pid=$1
    local cmd="$2"
    
    # 系统关键进程黑名单
    local blacklist=("systemd" "kernel" "init" "ssh" "sshd" "mysql" "mysqld" "launchd" "WindowServer")
    
    for process in "${blacklist[@]}"; do
        if [[ "$cmd" == *"$process"* ]]; then
            return 1
        fi
    done
    
    # 检查是否为系统用户进程
    local process_user=$(ps -p $pid -o user= 2>/dev/null || echo "")
    if [[ "$process_user" == "root" ]] && [[ "$cmd" != *"node"* ]] && [[ "$cmd" != *"npm"* ]] && [[ "$cmd" != *"pnpm"* ]]; then
        return 1
    fi
    
    return 0
}

verify_port_free() {
    local port=$1
    local max_attempts=5
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if ! lsof -ti:$port >/dev/null 2>&1; then
            log_success "✅ 端口 $port 已释放"
            return 0
        fi
        
        log_info "⏳ 等待端口 $port 释放... (尝试 $attempt/$max_attempts)"
        sleep 1
        ((attempt++))
    done
    
    log_error "❌ 端口 $port 释放失败"
    return 1
}

clean_all_ports() {
    log_step "🧹 清理所有项目端口"
    
    force_kill_port $SERVER_PORT "autobot-server"
    force_kill_port $FRONTEND_PORT "autobot-frontend"
    force_kill_port $ADMIN_PORT "autobot-admin"
    
    log_success "✅ 端口清理完成"
}

# ============================================================================
# 服务管理模块
# ============================================================================

start_services() {
    log_step "启动所有服务"

    # 检查必要的依赖
    if ! command -v pm2 >/dev/null 2>&1; then
        log_error "PM2 未安装，请先运行构建流程"
        return 1
    fi

    # 检查配置文件
    if [[ ! -f "$PROJECT_ROOT/ecosystem.config.js" ]]; then
        log_error "PM2 配置文件不存在: $PROJECT_ROOT/ecosystem.config.js"
        return 1
    fi

    # 检查环境配置
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        log_error "环境配置文件不存在，请先运行配置流程"
        return 1
    fi

    # 设置环境变量
    export PROJECT_ROOT="$PROJECT_ROOT"
    export NODE_ENV="production"

    cd "$PROJECT_ROOT"

    # 清理端口
    if ! clean_all_ports; then
        log_warning "端口清理失败，但继续启动服务"
    fi

    # 启动服务
    log_info "启动 PM2 服务..."
    if pm2 start ecosystem.config.js --env production; then
        log_success "服务启动命令执行成功"

        # 等待服务启动
        log_info "等待服务启动..."
        sleep 5

        # 检查服务状态
        if check_health; then
            log_success "所有服务启动成功"
            # 显示访问信息
            show_access_info
            return 0
        else
            log_warning "服务启动但健康检查失败，请检查日志"
            return 1
        fi
    else
        log_error "服务启动失败"
        log_info "尝试查看 PM2 日志获取更多信息: pm2 logs"
        return 1
    fi
}

stop_services() {
    log_step "停止所有服务"
    
    # 验证项目目录存在
    if [[ ! -d "$PROJECT_ROOT" ]]; then
        log_error "项目目录不存在: $PROJECT_ROOT"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # 检查 PM2 是否已安装
    if ! command -v pm2 >/dev/null 2>&1; then
        log_warning "PM2 未安装，跳过 PM2 进程停止"
    else
        # 检查是否有运行的进程
        local running_processes=$(pm2 list 2>/dev/null | grep -E "(autobot-server|autobot-frontend|autobot-admin)" | grep -c "online" || echo "0")
        
        if [[ "$running_processes" -gt 0 ]]; then
            log_info "检测到 $running_processes 个运行中的进程，正在停止..."
            
            # 停止 PM2 服务（添加错误处理但不中断执行）
            if pm2 stop ecosystem.config.js 2>/dev/null; then
                log_success "PM2 服务停止成功"
            else
                log_warning "PM2 服务停止失败或没有运行中的服务"
            fi
            
            # 删除 PM2 进程（添加错误处理但不中断执行）
            if pm2 delete ecosystem.config.js 2>/dev/null; then
                log_success "PM2 进程删除成功"
            else
                log_warning "PM2 进程删除失败或没有可删除的进程"
            fi
            
            # 等待进程完全关闭
            log_info "等待进程完全关闭..."
            sleep 2
            
            # 验证进程是否已停止
            local remaining_processes=$(pm2 list 2>/dev/null | grep -E "(autobot-server|autobot-frontend|autobot-admin)" | grep -c "online" || echo "0")
            if [[ "$remaining_processes" -eq 0 ]]; then
                log_success "所有服务已成功停止"
            else
                log_warning "仍有 $remaining_processes 个进程在运行，尝试强制清理..."
            fi
        else
            log_info "未检测到运行中的 PM2 进程"
        fi
    fi
    
    # 清理端口（无论 PM2 是否成功，都执行端口清理）
    log_info "执行端口清理..."
    clean_all_ports
    
    # 最终验证
    local still_running=0
    for port in $SERVER_PORT $FRONTEND_PORT $ADMIN_PORT; do
        if lsof -ti:$port >/dev/null 2>&1; then
            log_warning "端口 $port 仍被占用"
            ((still_running++))
        fi
    done
    
    if [[ "$still_running" -eq 0 ]]; then
        log_success "服务停止完成，所有端口已释放"
    else
        log_warning "服务停止完成，但仍有 $still_running 个端口被占用"
    fi
}

restart_services() {
    log_step "重启所有服务"
    
    # 先停止服务
    if stop_services; then
        log_info "服务停止成功，等待重启..."
        sleep 2
        start_services
    else
        log_error "服务停止失败，取消重启"
        return 1
    fi
}

check_health() {
    log_step "健康检查"
    
    local services=(
        "http://localhost:$SERVER_PORT/health:后端服务"
        "http://localhost:$FRONTEND_PORT:用户前台"
        "http://localhost:$ADMIN_PORT:管理后台"
    )
    
    for service in "${services[@]}"; do
        local url=$(echo $service | cut -d: -f1-2)
        local name=$(echo $service | cut -d: -f3)
        
        log_info "检查 $name ($url)..."
        
        if curl -s --max-time 10 "$url" >/dev/null 2>&1; then
            log_success "✅ $name 运行正常"
        else
            log_warning "⚠️  $name 可能未就绪"
        fi
    done
}

show_status() {
    log_step "显示服务状态"
    
    cd "$PROJECT_ROOT"
    
    # PM2 状态
    log_info "PM2 进程状态:"
    pm2 list
    
    # 端口状态
    log_info "\n端口占用状态:"
    for port in $SERVER_PORT $FRONTEND_PORT $ADMIN_PORT; do
        local pid=$(lsof -ti:$port 2>/dev/null || echo "")
        if [[ -n "$pid" ]]; then
            local cmd=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            log_success "端口 $port: 被进程 $pid ($cmd) 占用"
        else
            log_warning "端口 $port: 空闲"
        fi
    done
}

show_logs() {
    log_step "显示服务日志"
    
    cd "$PROJECT_ROOT"
    
    if ask_yes_no "是否显示实时日志？"; then
        pm2 logs
    else
        pm2 logs --lines 50
    fi
}

show_access_info() {
    echo -e "\n${BOLD}${GREEN}🎉 部署完成！${NC}"
    echo -e "${BOLD}${CYAN}访问地址：${NC}"
    echo -e "  ${ICON_SERVER} 用户前台: ${BLUE}http://localhost:$FRONTEND_PORT${NC}"
    echo -e "  ${ICON_GEAR} 管理后台: ${BLUE}http://localhost:$ADMIN_PORT${NC}"
    echo -e "  ${ICON_DATABASE} API 服务: ${BLUE}http://localhost:$SERVER_PORT${NC}"
    echo -e "\n${BOLD}${CYAN}管理命令：${NC}"
    echo -e "  ${ICON_CHECK} 查看状态: ${WHITE}./ab.sh status${NC}"
    echo -e "  ${ICON_RESTART} 重启服务: ${WHITE}./ab.sh restart${NC}"
    echo -e "  ${ICON_STOP} 停止服务: ${WHITE}./ab.sh stop${NC}"
    echo -e "  ${ICON_CLEAN} 清理端口: ${WHITE}./ab.sh clean${NC}"
}

# ============================================================================
# 主要功能函数
# ============================================================================

full_deploy() {
    log_step "${ICON_ROCKET} 开始完整部署流程"
    
    # 显示脚本信息
    echo -e "${BOLD}${CYAN}"
    echo "============================================================================"
    echo "  $SCRIPT_NAME v$SCRIPT_VERSION"
    echo "============================================================================"
    echo -e "${NC}"
    
    # 执行部署步骤
    local steps=(
        "check_os:系统环境检查"
        "check_permissions:权限检查"
        "check_tools:工具版本检查"
        "check_network:网络连接检查"
        "install_nodejs:Node.js 环境"
        "install_pnpm:pnpm 包管理器"
        "install_pm2:PM2 进程管理器"
        "check_mysql:MySQL 数据库"
        "set_project_root:项目根路径设置"
        "generate_env:环境配置生成"
        "check_mysql_service:MySQL 服务检查"
        "test_db_connection:数据库连接测试"
        "create_database:数据库创建"
        "install_dependencies:项目依赖安装"
        "build_packages:项目包构建"
        "verify_build:构建结果验证"
        "init_database:数据库初始化"
        "start_services:服务启动"
    )
    
    local total_steps=${#steps[@]}
    local current_step=0
    
    for step_info in "${steps[@]}"; do
        local step_func=$(echo $step_info | cut -d: -f1)
        local step_name=$(echo $step_info | cut -d: -f2)
        
        ((current_step++))
        
        echo -e "\n${BOLD}${PURPLE}[$current_step/$total_steps] $step_name${NC}"
        show_progress $current_step $total_steps
        echo
        
        if ! $step_func; then
            log_error "步骤失败: $step_name"
            exit 1
        fi
    done
    
    echo -e "\n${BOLD}${GREEN}🎊 AutoBot 部署完成！${NC}"
}

# ============================================================================
# 交互式菜单
# ============================================================================
# 新功能模块
# ============================================================================

# 项目依赖构建模块
build_project_dependencies() {
    log_step "开始项目依赖构建流程"
    
    # 系统环境检查
    check_os
    check_permissions
    check_network
    
    # 工具依赖安装
    install_nodejs
    install_pnpm
    install_pm2
    
    # 项目依赖安装
    install_dependencies
    
    # 项目构建
    build_packages
    
    # 构建验证
    verify_build
    
    log_success "项目依赖构建流程完成"
}

# 环境配置生成模块
generate_environment_config() {
    log_step "开始环境配置生成流程"

    # 设置项目根路径
    set_project_root

    # 生成基础.env文件（如果不存在）
    ensure_env_file_exists

    # 交互式数据库配置
    configure_env_interactive

    # 验证数据库设置
    validate_database_setup

    # 创建数据库
    create_database

    # 初始化数据库
    init_database

    # 验证最终配置
    validate_final_config

    log_success "环境配置生成流程完成"
}

# 确保.env文件存在
ensure_env_file_exists() {
    local env_file="$PROJECT_ROOT/.env"
    local env_example="$PROJECT_ROOT/.env.example"

    if [[ ! -f "$env_file" ]]; then
        if [[ -f "$env_example" ]]; then
            log_info "复制 .env.example 到 .env"
            cp "$env_example" "$env_file"
        else
            log_info "创建基础 .env 文件"
            create_basic_env
        fi
    fi
}

# 验证数据库设置
validate_database_setup() {
    log_step "验证数据库设置"

    # 直接测试数据库连接
    if test_db_connection; then
        log_success "数据库设置验证通过"
        return 0
    else
        log_error "数据库设置验证失败"
        return 1
    fi
}



# 验证最终配置
validate_final_config() {
    log_step "验证最终配置"

    local errors=()

    # 加载环境变量
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        source "$PROJECT_ROOT/.env"
    else
        errors+=("环境配置文件不存在")
    fi

    # 验证数据库配置
    [[ -z "${DB_HOST:-}" ]] && errors+=("数据库主机地址未设置")
    [[ -z "${DB_PORT:-}" ]] && errors+=("数据库端口未设置")
    [[ -z "${DB_USER:-}" ]] && errors+=("数据库用户名未设置")
    [[ -z "${DB_PASSWORD:-}" ]] && errors+=("数据库密码未设置")
    [[ -z "${DB_NAME:-}" ]] && errors+=("数据库名称未设置")

    # 验证管理员配置
    [[ -z "${DEFAULT_ADMIN_USERNAME:-}" ]] && errors+=("管理员用户名未设置")
    [[ -z "${DEFAULT_ADMIN_PASSWORD:-}" ]] && errors+=("管理员密码未设置")
    [[ -z "${DEFAULT_ADMIN_EMAIL:-}" ]] && errors+=("管理员邮箱未设置")

    # 验证管理员密码强度
    if [[ -n "${DEFAULT_ADMIN_PASSWORD:-}" ]] && [[ ${#DEFAULT_ADMIN_PASSWORD} -lt 8 ]]; then
        errors+=("管理员密码长度至少8位")
    fi

    if [[ ${#errors[@]} -gt 0 ]]; then
        log_error "配置验证失败："
        printf '  %s\n' "${errors[@]}"
        return 1
    fi

    log_success "配置验证通过"
    return 0
}

# 带重试的数据库连接测试
test_db_connection_with_retry() {
    log_step "测试数据库连接（带重试机制）"
    
    local max_retries=3
    local retry_count=0
    
    while [[ $retry_count -lt $max_retries ]]; do
        if test_db_connection; then
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        log_warning "数据库连接失败 (尝试 $retry_count/$max_retries)"
        
        if [[ $retry_count -lt $max_retries ]]; then
            if ask_yes_no "是否重新配置数据库连接？"; then
                configure_env_interactive
            else
                log_info "跳过重新配置，继续重试..."
                sleep 2
            fi
        fi
    done
    
    log_error "数据库连接失败，已达到最大重试次数"
    return 1
}

# 服务状态管理模块
service_management_menu() {
    while true; do
        clear
        echo -e "${BOLD}${CYAN}"
        echo "============================================================================"
        echo "  服务状态管理"
        echo "============================================================================"
        echo -e "${NC}"
        echo -e "${BOLD}${YELLOW}${ICON_MANAGE} 服务管理选项${NC}"
        echo
        echo -e "${BOLD}${CYAN}请选择要执行的操作：${NC}"
        echo
        echo -e "  ${BOLD}${GREEN} 1.${NC} ${ICON_START} 启动所有服务"
        echo -e "  ${BOLD}${GREEN} 2.${NC} ${ICON_RESTART} 重启所有服务"
        echo -e "  ${BOLD}${GREEN} 3.${NC} ${ICON_STOP} 停止所有服务"
        echo -e "  ${BOLD}${GREEN} 4.${NC} ${ICON_CHECK} 查看服务状态"
        echo -e "  ${BOLD}${GREEN} 5.${NC} ${ICON_LOG} 查看所有日志"
        echo -e "  ${BOLD}${GREEN} 6.${NC} ${ICON_GEAR} 特定服务管理"
        echo -e "  ${BOLD}${GREEN} 7.${NC} ${ICON_CLEAN} 删除所有进程"
        echo -e "  ${BOLD}${RED} 8.${NC} ${ICON_EXIT} 返回主菜单"
        echo
        echo -e "${BOLD}${CYAN}端口配置：${NC}"
        echo -e "  ${ICON_SERVER} 后端服务: ${BLUE}$SERVER_PORT${NC}"
        echo -e "  ${ICON_FRONTEND} 用户前台: ${BLUE}$FRONTEND_PORT${NC}"
        echo -e "  ${ICON_ADMIN} 管理后台: ${BLUE}$ADMIN_PORT${NC}"
        echo
        
        echo -ne "${BOLD}${YELLOW}请输入选项编号 (1-8): ${NC}"
        read -r choice
        echo
        
        case $choice in
            1)
                log_info "用户选择: 启动所有服务"
                start_services
                ;;
            2)
                log_info "用户选择: 重启所有服务"
                restart_services
                ;;
            3)
                log_info "用户选择: 停止所有服务"
                stop_services
                ;;
            4)
                log_info "用户选择: 查看服务状态"
                show_status
                ;;
            5)
                log_info "用户选择: 查看所有日志"
                show_logs
                ;;
            6)
                log_info "用户选择: 特定服务管理"
                specific_service_menu
                ;;
            7)
                log_info "用户选择: 删除所有进程"
                delete_all_processes
                ;;
            8)
                log_info "用户选择: 返回主菜单"
                return 0
                ;;
            *)
                echo -e "${RED}${ICON_ERROR} 无效选项，请输入 1-8 之间的数字${NC}"
                ;;
        esac
        
        if [[ $choice != "8" ]]; then
            echo
            echo -e "${BOLD}${CYAN}操作完成！${NC}"
            echo -ne "${BOLD}${YELLOW}按回车键继续...${NC}"
            read -r
        fi
    done
}

# 特定服务管理菜单
specific_service_menu() {
    while true; do
        clear
        echo -e "${BOLD}${CYAN}"
        echo "============================================================================"
        echo "  特定服务管理"
        echo "============================================================================"
        echo -e "${NC}"
        echo -e "${BOLD}${YELLOW}${ICON_GEAR} 选择要管理的服务${NC}"
        echo
        echo -e "${BOLD}${CYAN}请选择服务：${NC}"
        echo
        echo -e "  ${BOLD}${GREEN} 1.${NC} ${ICON_SERVER} 后端服务 (autobot-server)"
        echo -e "  ${BOLD}${GREEN} 2.${NC} ${ICON_FRONTEND} 用户前台 (autobot-frontend)"
        echo -e "  ${BOLD}${GREEN} 3.${NC} ${ICON_ADMIN} 管理后台 (autobot-admin)"
        echo -e "  ${BOLD}${RED} 4.${NC} ${ICON_EXIT} 返回上级菜单"
        echo
        
        echo -ne "${BOLD}${YELLOW}请输入选项编号 (1-4): ${NC}"
        read -r choice
        echo
        
        case $choice in
            1)
                manage_specific_service "autobot-server" "后端服务"
                ;;
            2)
                manage_specific_service "autobot-frontend" "用户前台"
                ;;
            3)
                manage_specific_service "autobot-admin" "管理后台"
                ;;
            4)
                return 0
                ;;
            *)
                echo -e "${RED}${ICON_ERROR} 无效选项，请输入 1-4 之间的数字${NC}"
                echo -ne "${BOLD}${YELLOW}按回车键继续...${NC}"
                read -r
                ;;
        esac
    done
}

# 管理特定服务
manage_specific_service() {
    local service_name="$1"
    local service_display="$2"
    
    # 检查 PM2 是否已安装
    if ! command -v pm2 >/dev/null 2>&1; then
        log_error "PM2 未安装，无法进行服务管理"
        echo -ne "${BOLD}${YELLOW}按回车键继续...${NC}"
        read -r
        return 1
    fi
    
    while true; do
        clear
        echo -e "${BOLD}${CYAN}"
        echo "============================================================================"
        echo "  $service_display 管理"
        echo "============================================================================"
        echo -e "${NC}"
        echo -e "${BOLD}${YELLOW}${ICON_GEAR} $service_display ($service_name)${NC}"
        echo
        
        # 显示当前状态
        local status_info=$(pm2 show "$service_name" 2>/dev/null | grep -E "status|restart|uptime|memory" || echo "服务未运行")
        echo -e "${BOLD}${CYAN}当前状态：${NC}"
        echo -e "$status_info"
        echo
        
        echo -e "${BOLD}${CYAN}请选择操作：${NC}"
        echo
        echo -e "  ${BOLD}${GREEN} 1.${NC} ${ICON_START} 启动服务"
        echo -e "  ${BOLD}${GREEN} 2.${NC} ${ICON_RESTART} 重启服务"
        echo -e "  ${BOLD}${GREEN} 3.${NC} ${ICON_STOP} 停止服务"
        echo -e "  ${BOLD}${GREEN} 4.${NC} ${ICON_CHECK} 查看详细状态"
        echo -e "  ${BOLD}${GREEN} 5.${NC} ${ICON_LOG} 查看日志"
        echo -e "  ${BOLD}${RED} 6.${NC} ${ICON_EXIT} 返回上级菜单"
        echo
        
        echo -ne "${BOLD}${YELLOW}请输入选项编号 (1-6): ${NC}"
        read -r choice
        echo
        
        case $choice in
            1)
                log_info "启动 $service_display..."
                if pm2 start "$service_name" 2>/dev/null; then
                    log_success "$service_display 启动成功"
                else
                    log_error "$service_display 启动失败（可能已运行或配置错误）"
                fi
                ;;
            2)
                log_info "重启 $service_display..."
                if pm2 restart "$service_name" 2>/dev/null; then
                    log_success "$service_display 重启成功"
                else
                    log_error "$service_display 重启失败（可能未运行或配置错误）"
                fi
                ;;
            3)
                log_info "停止 $service_display..."
                if pm2 stop "$service_name" 2>/dev/null; then
                    log_success "$service_display 停止成功"
                else
                    log_error "$service_display 停止失败（可能未运行）"
                fi
                ;;
            4)
                log_info "查看 $service_display 状态..."
                if pm2 show "$service_name" 2>/dev/null; then
                    log_success "状态信息获取成功"
                else
                    log_error "$service_display 未找到或状态获取失败"
                fi
                ;;
            5)
                log_info "查看 $service_display 日志..."
                if pm2 logs "$service_name" --lines 50 2>/dev/null; then
                    log_success "日志获取成功"
                else
                    log_error "$service_display 日志获取失败"
                fi
                ;;
            6)
                return 0
                ;;
            *)
                echo -e "${RED}${ICON_ERROR} 无效选项，请输入 1-6 之间的数字${NC}"
                ;;
        esac
        
        if [[ $choice != "6" ]]; then
            echo
            echo -e "${BOLD}${CYAN}操作完成！${NC}"
            echo -ne "${BOLD}${YELLOW}按回车键继续...${NC}"
            read -r
        fi
    done
}

# 删除所有进程
delete_all_processes() {
    log_step "删除所有PM2进程"
    
    if ask_yes_no "确定要删除所有PM2进程吗？这将停止所有服务"; then
        log_info "停止所有PM2进程..."
        if pm2 stop all 2>/dev/null; then
            log_success "所有PM2进程已停止"
        else
            log_warning "停止进程时遇到问题或没有运行中的进程"
        fi
        
        log_info "删除所有PM2进程..."
        if pm2 delete all 2>/dev/null; then
            log_success "所有PM2进程已删除"
        else
            log_warning "删除进程时遇到问题或没有可删除的进程"
        fi
        
        log_info "清理端口..."
        clean_all_ports
        
        # 验证清理结果
        local remaining_processes=$(pm2 list 2>/dev/null | grep -E "(autobot-server|autobot-frontend|autobot-admin)" | grep -c "online" || echo "0")
        if [[ "$remaining_processes" -eq 0 ]]; then
            log_success "所有进程已删除"
        else
            log_warning "仍有 $remaining_processes 个进程在运行"
        fi
    else
        log_info "操作已取消"
    fi
}

# ============================================================================
# 菜单显示和交互
# ============================================================================

show_menu() {
    clear
    echo -e "${BOLD}${CYAN}"
    echo "============================================================================"
    echo "  $SCRIPT_NAME v$SCRIPT_VERSION"
    echo "============================================================================"
    echo -e "${NC}"
    echo -e "${BOLD}${YELLOW}${ICON_GEAR} AutoBot 智能部署管理菜单${NC}"
    echo
    echo -e "${BOLD}${CYAN}请选择要执行的操作：${NC}"
    echo
    echo -e "  ${BOLD}${GREEN} 1.${NC} ${ICON_BUILD} 项目依赖构建"
    echo -e "  ${BOLD}${GREEN} 2.${NC} ${ICON_GEAR} 环境配置生成"
    echo -e "  ${BOLD}${GREEN} 3.${NC} ${ICON_MANAGE} 服务状态管理"
    echo -e "  ${BOLD}${RED} 4.${NC} ${ICON_EXIT} 退出"
    echo
    echo -e "${BOLD}${CYAN}端口配置：${NC}"
    echo -e "  ${ICON_SERVER} 后端服务: ${BLUE}$SERVER_PORT${NC}"
    echo -e "  ${ICON_FRONTEND} 用户前台: ${BLUE}$FRONTEND_PORT${NC}"
    echo -e "  ${ICON_ADMIN} 管理后台: ${BLUE}$ADMIN_PORT${NC}"
    echo
}

interactive_menu() {
    while true; do
        show_menu
        echo -ne "${BOLD}${YELLOW}请输入选项编号 (1-4): ${NC}"
        read -r choice
        echo
        
        case $choice in
            1)
                log_info "用户选择: 项目依赖构建"
                build_project_dependencies
                ;;
            2)
                log_info "用户选择: 环境配置生成"
                generate_environment_config
                ;;
            3)
                log_info "用户选择: 服务状态管理"
                service_management_menu
                ;;
            4)
                log_info "用户选择: 退出"
                echo -e "${BOLD}${GREEN}${ICON_EXIT} 感谢使用 AutoBot 智能部署脚本！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}${ICON_ERROR} 无效选项，请输入 1-4 之间的数字${NC}"
                ;;
        esac
        
        if [[ $choice != "4" ]]; then
            echo
            echo -e "${BOLD}${CYAN}操作完成！${NC}"
            echo -ne "${BOLD}${YELLOW}按回车键返回主菜单...${NC}"
            read -r
        fi
    done
}

# ============================================================================
# 帮助和使用说明
# ============================================================================

show_help() {
    echo -e "${BOLD}${CYAN}$SCRIPT_NAME v$SCRIPT_VERSION${NC}"
    echo -e "${BOLD}用法：${NC}"
    echo -e "  ./ab.sh [命令] [选项]"
    echo
    echo -e "${BOLD}主要功能模块：${NC}"
    echo -e "  ${GREEN}build${NC}       ${ICON_BUILD} 项目依赖构建 (系统环境检查、工具安装、依赖安装、项目构建)"
    echo -e "  ${GREEN}config${NC}      ${ICON_GEAR} 环境配置生成 (数据库配置、连接验证、环境文件生成)"
    echo -e "  ${GREEN}manage${NC}      ${ICON_MANAGE} 服务状态管理 (启动/停止/重启服务、状态查看、日志查看)"
    echo
    echo -e "${BOLD}传统命令（兼容性保留）：${NC}"
    echo -e "  ${GREEN}deploy${NC}      完整部署流程"
    echo -e "  ${GREEN}install${NC}     仅安装依赖"
    echo -e "  ${GREEN}database${NC}    仅数据库操作"
    echo -e "  ${GREEN}start${NC}       启动服务"
    echo -e "  ${GREEN}stop${NC}        停止服务"
    echo -e "  ${GREEN}restart${NC}     重启服务"
    echo -e "  ${GREEN}status${NC}      查看状态"
    echo -e "  ${GREEN}logs${NC}        查看日志"
    echo -e "  ${GREEN}clean${NC}       清理端口"
    echo -e "  ${GREEN}help${NC}        显示帮助"
    echo -e "  ${GREEN}menu${NC}        显示交互式菜单"
    echo
    echo -e "${BOLD}示例：${NC}"
    echo -e "  ./ab.sh                 # 显示交互式菜单"
    echo -e "  ./ab.sh build           # 项目依赖构建"
    echo -e "  ./ab.sh config          # 环境配置生成"
    echo -e "  ./ab.sh manage          # 服务状态管理"
    echo -e "  ./ab.sh deploy          # 完整部署（传统模式）"
    echo
    echo -e "${BOLD}端口配置：${NC}"
    echo -e "  ${ICON_SERVER} 后端服务: ${BLUE}$SERVER_PORT${NC}"
    echo -e "  ${ICON_FRONTEND} 用户前台: ${BLUE}$FRONTEND_PORT${NC}"
    echo -e "  ${ICON_ADMIN} 管理后台: ${BLUE}$ADMIN_PORT${NC}"
}

show_usage() {
    echo -e "${RED}错误：未知命令 '$1'${NC}"
    echo -e "使用 ${WHITE}./ab.sh help${NC} 查看帮助"
    exit 1
}

# ============================================================================
# 主程序入口
# ============================================================================

main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 记录开始时间
    log_info "脚本开始执行: $SCRIPT_NAME v$SCRIPT_VERSION"
    log_info "执行用户: $(whoami)"
    log_info "工作目录: $PROJECT_ROOT"
    
    # 处理命令行参数
    case "${1:-}" in
        "")
            # 没有参数时显示交互式菜单
            interactive_menu
            ;;
        "menu")
            # 显示交互式菜单
            interactive_menu
            ;;
        "build")
            # 项目依赖构建
            build_project_dependencies
            ;;
        "config")
            # 环境配置生成
            generate_environment_config
            ;;
        "manage")
            # 服务状态管理
            service_management_menu
            ;;
        "deploy")
            # 完整部署流程（传统模式）
            full_deploy
            ;;
        "install")
            # 仅安装依赖（传统模式）
            install_nodejs && install_pnpm && install_pm2 && install_dependencies
            ;;
        "database")
            # 仅数据库操作（传统模式）
            check_mysql && check_mysql_service && test_db_connection && create_database && init_database
            ;;
        "start")
            # 启动服务
            start_services
            ;;
        "stop")
            # 停止服务
            stop_services
            ;;
        "restart")
            # 重启服务
            restart_services
            ;;
        "status")
            # 查看状态
            show_status
            ;;
        "logs")
            # 查看日志
            show_logs
            ;;
        "clean")
            # 清理端口
            clean_all_ports
            ;;
        "help"|"-h"|"--help")
            # 显示帮助
            show_help
            ;;
        *)
            show_usage "$1"
            ;;
    esac
    
    # 记录结束时间
    log_info "脚本执行完成"
}

# 错误处理
trap 'log_error "脚本执行中断"; exit 1' INT TERM

# 执行主程序
main "$@"