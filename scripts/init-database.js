#!/usr/bin/env node

// AutoBot 数据库初始化脚本
// 使用统一配置管理，避免硬编码

const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')

// 使用统一配置管理
const config = require('../packages/shared/config')

// 从统一配置获取数据库配置
const dbConfig = config.database

console.log('🔧 AutoBot 数据库初始化')
console.log('📡 连接信息:', {
  host: dbConfig.host,
  port: dbConfig.port,
  user: dbConfig.user,
  database: dbConfig.database
})

async function initDatabase() {
  let connection

  try {
    // 连接数据库
    console.log('\n📡 正在连接数据库...')
    connection = await mysql.createConnection(dbConfig)
    console.log('✅ 数据库连接成功')

    // 创建应用表
    console.log('\n📋 创建应用表...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS apps (
        id VARCHAR(255) PRIMARY KEY,
        app_data TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ 应用表创建成功')

    // 创建用户表
    console.log('\n👥 创建用户表...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        nickname VARCHAR(100),
        phone VARCHAR(20),
        password_hash VARCHAR(255) NOT NULL,
        points INT DEFAULT 100,
        status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        login_attempts INT DEFAULT 0,
        locked_until TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ 用户表创建成功')

    // 创建管理员表
    console.log('\n🔐 创建管理员表...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ 管理员表创建成功')

    // 创建系统配置表
    console.log('\n⚙️ 创建系统配置表...')
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        config_key VARCHAR(100) UNIQUE NOT NULL,
        config_value TEXT,
        description VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_config_key (config_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ 系统配置表创建成功')

    // 检查是否已有管理员
    console.log('\n🔍 检查管理员账户...')
    const [adminRows] = await connection.execute('SELECT COUNT(*) as count FROM admins')
    const adminCount = adminRows[0].count

    // 定义默认密码变量在外层作用域
    const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123'

    if (adminCount === 0) {
      // 创建默认管理员
      console.log('👤 创建默认管理员账户...')
      const hashedPassword = await bcrypt.hash(defaultPassword, 12)
      
      await connection.execute(`
        INSERT INTO admins (username, password_hash, email) 
        VALUES (?, ?, ?)
      `, [
        process.env.DEFAULT_ADMIN_USERNAME || 'admin',
        hashedPassword,
        process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>'
      ])
      
      console.log('✅ 默认管理员创建成功')
      console.log(`   用户名: ${process.env.DEFAULT_ADMIN_USERNAME || 'admin'}`)
      console.log(`   密码: ${defaultPassword}`)
    } else {
      console.log('ℹ️  管理员账户已存在，跳过创建')
    }

    // 插入系统配置
    console.log('\n⚙️ 初始化系统配置...')
    const systemConfigs = [
      ['dify_chat_version', '0.4.0', 'Dify-Chat 版本'],
      ['autobot_version', '1.0.0', 'AutoBot 版本'],
      ['system_initialized', 'true', '系统是否已初始化'],
      ['max_file_size', '20971520', '最大文件上传大小（字节）'],
      ['default_user_points', '100', '新用户默认积分']
    ]

    for (const [key, value, description] of systemConfigs) {
      await connection.execute(`
        INSERT INTO system_config (config_key, config_value, description) 
        VALUES (?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
        config_value = VALUES(config_value),
        description = VALUES(description),
        updated_at = CURRENT_TIMESTAMP
      `, [key, value, description])
    }
    console.log('✅ 系统配置初始化完成')

    // 创建示例应用
    console.log('\n🎯 创建示例应用...')
    const sampleApp = {
      id: 'sample-chat-app',
      name: '示例聊天应用',
      description: '这是一个示例聊天应用，展示 AutoBot 的基本功能',
      mode: 'chat',
      status: 'active',
      info: {
        tags: ['示例', '聊天', 'AI'],
        points_cost: 1,
        created_by: 'system'
      }
    }

    await connection.execute(`
      INSERT INTO apps (id, app_data) 
      VALUES (?, ?) 
      ON DUPLICATE KEY UPDATE 
      app_data = VALUES(app_data),
      updated_at = CURRENT_TIMESTAMP
    `, [sampleApp.id, JSON.stringify(sampleApp)])
    console.log('✅ 示例应用创建成功')

    // 验证数据
    console.log('\n🔍 验证数据完整性...')
    const [appCount] = await connection.execute('SELECT COUNT(*) as count FROM apps')
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users')
    const [adminCountFinal] = await connection.execute('SELECT COUNT(*) as count FROM admins')
    const [configCount] = await connection.execute('SELECT COUNT(*) as count FROM system_config')

    console.log('📊 数据统计:')
    console.log(`   应用数量: ${appCount[0].count}`)
    console.log(`   用户数量: ${userCount[0].count}`)
    console.log(`   管理员数量: ${adminCountFinal[0].count}`)
    console.log(`   配置项数量: ${configCount[0].count}`)

    console.log('\n🎉 数据库初始化完成！')
    console.log('\n📋 接下来可以：')
    console.log('   1. 启动后端服务: pnpm run dev:api')
    console.log('   2. 启动前端应用: pnpm run dev:frontend')
    console.log('   3. 启动管理后台: pnpm run dev:admin')
    console.log(`   4. 访问前端应用: http://localhost:${process.env.FRONTEND_PORT || 5200}`)
    console.log(`   5. 访问管理后台: http://localhost:${process.env.ADMIN_PORT || 5202}`)
    console.log(`   6. 使用管理员账户登录: ${process.env.DEFAULT_ADMIN_USERNAME || 'admin'} / ${defaultPassword || 'admin123'}`)

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 请检查数据库连接信息是否正确')
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 请检查数据库用户名和密码是否正确')
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 请检查数据库名称是否正确')
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log('📡 数据库连接已关闭')
    }
  }
}

// 运行初始化
initDatabase().catch(error => {
  console.error('💥 初始化过程中发生错误:', error)
  process.exit(1)
})
