// 环境配置管理
const config = {
  // 环境类型
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // 服务端口
  PORT: process.env.PORT || 3008,
  
  // 数据库配置
  database: {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 20,
    queueLimit: parseInt(process.env.DB_QUEUE_LIMIT) || 0,
    // MySQL2支持的正确配置选项
    connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT) || 60000,
    timezone: '+00:00',
    charset: 'utf8mb4',
    multipleStatements: false
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  
  // 默认管理员配置
  defaultAdmin: {
    username: process.env.DEFAULT_ADMIN_USERNAME,
    password: process.env.DEFAULT_ADMIN_PASSWORD,
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs',
  },
  
  // 监控配置
  monitoring: {
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000,
  },
  
  // Redis缓存配置 - 移除硬编码
  redis: {
    host: process.env.REDIS_HOST || null, // 不设置默认值，让缓存模块决定是否使用Redis
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || null,
    db: parseInt(process.env.REDIS_DB) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'autobot:',
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableOfflineQueue: false,
  },
  
  // 缓存配置
  cache: {
    // 应用列表缓存时间（秒）
    appListTTL: parseInt(process.env.CACHE_APP_LIST_TTL) || 300, // 5分钟
    // 应用详情缓存时间（秒）
    appDetailTTL: parseInt(process.env.CACHE_APP_DETAIL_TTL) || 600, // 10分钟
    // 健康检查缓存时间（秒）
    healthCheckTTL: parseInt(process.env.CACHE_HEALTH_TTL) || 30, // 30秒
    // 系统设置缓存时间（秒）
    systemSettingsTTL: parseInt(process.env.CACHE_SYSTEM_SETTINGS_TTL) || 300, // 5分钟
  },
  
  // 任务调度配置
  scheduler: {
    // 数据库备份频率
    backupCron: process.env.BACKUP_CRON || '0 2 * * *', // 每天凌晨2点
    // 日志清理频率
    logCleanupCron: process.env.LOG_CLEANUP_CRON || '0 3 * * 0', // 每周日凌晨3点
    // 性能统计频率
    statsCron: process.env.STATS_CRON || '*/5 * * * *', // 每5分钟
  },
  
  // 生产环境检查
  isProduction: () => config.NODE_ENV === 'production',
  isDevelopment: () => config.NODE_ENV === 'development',
}

module.exports = config