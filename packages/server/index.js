// 首先加载环境变量配置（必须在其他模块加载之前）
require('dotenv').config()

const Koa = require('koa')
const bodyParser = require('koa-bodyparser')
const Router = require('koa-router')
const cors = require('koa-cors')
const mysql = require('mysql2/promise')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const rateLimit = require('koa-ratelimit')
const compress = require('koa-compress')
const path = require('path')
const multer = require('@koa/multer')
const fs = require('fs')
const serve = require('koa-static')

// 导入配置和日志系统
const config = require('./config')
const sharedConfig = require('../shared/config') // 使用统一配置
const logger = require('./logger')
const cache = require('./cache')
const scheduler = require('./scheduler')

// JWT配置
const { secret: JWT_SECRET, expiresIn: JWT_EXPIRES_IN } = config.jwt

// 初始化应用
const app = new Koa()
const router = new Router()

// 请求日志中间件
app.use(async (ctx, next) => {
	const startTime = Date.now()
	try {
		await next()
		logger.logRequest(ctx, startTime)
	} catch (error) {
		logger.logError(error, {
			method: ctx.method,
			url: ctx.url,
			userAgent: ctx.headers['user-agent'],
			ip: ctx.ip
		})
		throw error
	}
})

// 错误处理中间件
app.use(async (ctx, next) => {
	try {
		await next()
	} catch (error) {
		logger.logError(error, {
			method: ctx.method,
			url: ctx.url,
			body: ctx.request.body
		})
		
		// 不暴露内部错误信息到客户端
		const message = config.isProduction() 
			? '服务器内部错误' 
			: error.message
			
		ctx.status = error.status || 500
		ctx.body = {
			message,
			timestamp: new Date().toISOString()
		}
	}
})

// 基础中间件
app.use(bodyParser({
	jsonLimit: '10mb', // 限制请求体大小
	formLimit: '10mb',
	enableTypes: ['json', 'form']
}))

app.use(cors({
	origin: sharedConfig.cors.origins, // 使用统一配置的CORS来源
	credentials: true,
	maxAge: 86400 // 24小时
}))

// 压缩中间件
app.use(compress({
	threshold: 1024,
	gzip: {
		flush: require('zlib').constants.Z_SYNC_FLUSH,
	},
	deflate: {
		flush: require('zlib').constants.Z_SYNC_FLUSH,
	},
	br: false // 暂时禁用brotli
}))

// 限流中间件
const limiterConfig = {
	driver: 'memory',
	db: new Map(),
	duration: 60000, // 1分钟
	errorMessage: '请求过于频繁，请稍后再试',
	id: (ctx) => ctx.ip,
	headers: {
		remaining: 'Rate-Limit-Remaining',
		reset: 'Rate-Limit-Reset',
		total: 'Rate-Limit-Total'
	}
}

// 不同端点的限流配置
app.use(rateLimit({
	...limiterConfig,
	max: 100, // 一般接口每分钟100次
	skip: (ctx) => {
		// 登录接口更严格的限流
		if (ctx.path === '/admin/login') {
			return false
		}
		return false
	}
}))

// 登录接口特殊限流
router.use('/admin/login', rateLimit({
	...limiterConfig,
	max: 5, // 登录接口每分钟5次
	message: '登录尝试过于频繁，请1分钟后再试'
}))

// 静态文件服务将在路由注册后添加

// MySQL连接配置
const DB_CONFIG = config.database

// 创建MySQL连接池
const pool = mysql.createPool(DB_CONFIG)

// 文件上传配置 - 使用统一配置，避免硬编码
const uploadDir = sharedConfig.paths.icons
if (!fs.existsSync(uploadDir)) {
	fs.mkdirSync(uploadDir, { recursive: true })
}

const storage = multer.diskStorage({
	destination: (req, file, cb) => {
		cb(null, uploadDir)
	},
	filename: (req, file, cb) => {
		const ext = path.extname(file.originalname)
		const timestamp = Date.now()
		const randomStr = Math.random().toString(36).substring(2, 8)
		cb(null, `${timestamp}-${randomStr}${ext}`)
	}
})

const upload = multer({
	storage: storage,
	limits: {
		fileSize: 2 * 1024 * 1024, // 2MB限制
	},
	fileFilter: (req, file, cb) => {
		// 只允许图片文件
		const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml', 'image/x-icon']
		if (allowedTypes.includes(file.mimetype)) {
			cb(null, true)
		} else {
			cb(new Error('只允许上传图片文件'))
		}
	}
})

// 初始化数据库表
const initDatabase = async () => {
	const startTime = Date.now()
	try {
		const connection = await pool.getConnection()
		
		// 检查现有的apps表，如果不存在则创建
		await connection.execute(`
			CREATE TABLE IF NOT EXISTS apps (
				id VARCHAR(255) PRIMARY KEY,
				app_data TEXT NOT NULL,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				INDEX idx_created_at (created_at)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`)

		// 创建管理员表
		await connection.execute(`
			CREATE TABLE IF NOT EXISTS admins (
				id INT AUTO_INCREMENT PRIMARY KEY,
				username VARCHAR(50) UNIQUE NOT NULL,
				password_hash VARCHAR(255) NOT NULL,
				last_login TIMESTAMP NULL,
				login_attempts INT DEFAULT 0,
				locked_until TIMESTAMP NULL,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				INDEX idx_username (username)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`)

		// 创建系统设置表
		await connection.execute(`
			CREATE TABLE IF NOT EXISTS system_settings (
				id INT AUTO_INCREMENT PRIMARY KEY,
				setting_key VARCHAR(100) UNIQUE NOT NULL,
				setting_value TEXT NOT NULL,
				setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
				description VARCHAR(500) NULL,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				INDEX idx_setting_key (setting_key)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`)

		// 创建用户表
		await connection.execute(`
			CREATE TABLE IF NOT EXISTS users (
				id INT AUTO_INCREMENT PRIMARY KEY,
				username VARCHAR(50) UNIQUE NOT NULL,
				email VARCHAR(100) UNIQUE NOT NULL,
				password_hash VARCHAR(255) NOT NULL,
				nickname VARCHAR(100),
				avatar_url VARCHAR(500),
				phone VARCHAR(20),
				points INT DEFAULT 100 COMMENT '用户积分，默认100',
				status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
				last_login TIMESTAMP NULL,
				login_attempts INT DEFAULT 0,
				locked_until TIMESTAMP NULL,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				INDEX idx_username (username),
				INDEX idx_email (email)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
		`)

		// 检查并添加积分字段（兼容已存在的用户表）
		try {
			await connection.execute(`
				ALTER TABLE users ADD COLUMN points INT DEFAULT 100 COMMENT '用户积分，默认100'
			`)
			logger.info('为现有用户表添加积分字段成功')
		} catch (error) {
			// 字段已存在，忽略错误
			if (!error.message.includes('Duplicate column name')) {
				logger.logError(error, { operation: 'add_points_column' })
			}
		}

		// 检查是否已有管理员，如果没有则创建默认管理员
		const [adminRows] = await connection.execute('SELECT COUNT(*) as count FROM admins')
		if (adminRows[0].count === 0) {
			const hashedPassword = await bcrypt.hash(config.defaultAdmin.password, 10)
			await connection.execute(
				'INSERT INTO admins (username, password_hash) VALUES (?, ?)',
				[config.defaultAdmin.username, hashedPassword]
			)
			logger.info('默认管理员账号已创建', {
				username: config.defaultAdmin.username,
				password: config.defaultAdmin.password
			})
		}

		// 初始化默认系统设置
		const defaultSettings = [
			{
				key: 'default_theme',
				value: 'light',
				type: 'string',
				description: '系统默认主题模式 (light/dark/system)'
			},
			{
				key: 'site_title',
				value: 'DifyChat',
				type: 'string',
				description: '网站标题'
			},
			{
				key: 'favicon_admin',
				value: '/favicon.ico',
				type: 'string',
				description: '管理后台Favicon图标路径'
			},
			{
				key: 'favicon_frontend',
				value: '/favicon.ico',
				type: 'string',
				description: '前台Favicon图标路径'
			},
			{
				key: 'logo_frontend',
				value: '/logo.png',
				type: 'string',
				description: '前台Logo图标路径'
			}
		]

		for (const setting of defaultSettings) {
			await connection.execute(`
				INSERT IGNORE INTO system_settings 
				(setting_key, setting_value, setting_type, description) 
				VALUES (?, ?, ?, ?)
			`, [setting.key, setting.value, setting.type, setting.description])
		}

		connection.release()
		logger.logDatabaseOperation('INIT', 'all_tables', Date.now() - startTime)
		logger.info('数据库初始化完成')
	} catch (error) {
		logger.logError(error, { operation: 'database_init' })
		logger.logDatabaseOperation('INIT', 'all_tables', Date.now() - startTime, false)
		throw error
	}
}

// 启动时初始化系统
const initializeSystem = async () => {
	try {
		logger.info('开始系统初始化...')
		
		// 首先初始化缓存系统（确保其他模块可以使用缓存）
		logger.info('初始化缓存系统...')
		await cache.connect()
		
		// 初始化数据库
		logger.info('初始化数据库...')
		await initDatabase()
		
		// 初始化任务调度器
		logger.info('初始化任务调度器...')
		await scheduler.init()
		
		logger.info('系统初始化完成')
		return true
	} catch (error) {
		logger.logError(error, { operation: 'system_init' })
		// 不要直接抛出错误，而是返回false表示失败
		return false
	}
}

// JWT验证中间件
const authenticateToken = async (ctx, next) => {
	const authHeader = ctx.headers.authorization
	const token = authHeader && authHeader.split(' ')[1] // Bearer TOKEN

	if (!token) {
		ctx.status = 401
		ctx.body = { 
			message: '缺少认证token',
			timestamp: new Date().toISOString()
		}
		return
	}

	try {
		const decoded = jwt.verify(token, config.jwt.secret)
		ctx.user = decoded
		await next()
	} catch (error) {
		logger.logError(error, { 
			operation: 'token_verification',
			ip: ctx.ip,
			userAgent: ctx.headers['user-agent']
		})
		
		ctx.status = 403
		ctx.body = { 
			message: 'token无效或已过期',
			timestamp: new Date().toISOString()
		}
	}
}

// ==================== 用户相关API ====================

// 用户注册
router.post('/api/users/register', async ctx => {
	try {
		const { username, email, password, nickname } = ctx.request.body

		// 基础验证
		if (!username || !email || !password) {
			ctx.status = 400
			ctx.body = { message: '用户名、邮箱和密码不能为空' }
			return
		}

		// 验证用户名长度
		if (username.length < 3 || username.length > 20) {
			ctx.status = 400
			ctx.body = { message: '用户名长度必须在3-20个字符之间' }
			return
		}

		// 验证密码长度
		if (password.length < 6) {
			ctx.status = 400
			ctx.body = { message: '密码长度不能少于6个字符' }
			return
		}

		// 验证邮箱格式
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
		if (!emailRegex.test(email)) {
			ctx.status = 400
			ctx.body = { message: '邮箱格式不正确' }
			return
		}

		const connection = await pool.getConnection()

		// 检查用户名是否已存在
		const [existingUsers] = await connection.execute(
			'SELECT id FROM users WHERE username = ? OR email = ?',
			[username, email]
		)

		if (existingUsers.length > 0) {
			connection.release()
			ctx.status = 409
			ctx.body = { message: '用户名或邮箱已存在' }
			return
		}

		// 加密密码
		const passwordHash = await bcrypt.hash(password, 10)

		// 创建用户
		const [result] = await connection.execute(
			'INSERT INTO users (username, email, password_hash, nickname, status, points) VALUES (?, ?, ?, ?, ?, ?)',
			[username, email, passwordHash, nickname || username, 'active', 100]
		)

		// 获取新创建的用户信息（包含默认积分）
		const [newUserRows] = await connection.execute(
			'SELECT id, username, email, nickname, avatar_url, phone, points, created_at FROM users WHERE id = ?',
			[result.insertId]
		)

		const newUser = newUserRows[0]
		connection.release()

		// 生成JWT token
		const token = jwt.sign(
			{ id: result.insertId, username, type: 'user' },
			JWT_SECRET,
			{ expiresIn: JWT_EXPIRES_IN }
		)

		ctx.status = 201
		ctx.body = {
			message: '注册成功',
			token,
			user: {
				id: newUser.id,
				username: newUser.username,
				email: newUser.email,
				nickname: newUser.nickname,
				avatar_url: newUser.avatar_url,
				phone: newUser.phone,
				points: newUser.points || 100,
				created_at: newUser.created_at
			}
		}

		logger.info(`🎉 用户注册成功: ${username}`)
	} catch (error) {
		logger.logError(error, { operation: 'user_register' })
		ctx.status = 500
		ctx.body = { message: '注册失败，请稍后重试' }
	}
})

// 用户登录
router.post('/api/users/login', async ctx => {
	try {
		const { username, password } = ctx.request.body

		if (!username || !password) {
			ctx.status = 400
			ctx.body = { message: '用户名和密码不能为空' }
			return
		}

		const connection = await pool.getConnection()

		// 支持用户名或邮箱登录
		const [rows] = await connection.execute(
			'SELECT id, username, email, password_hash, nickname, phone, points, status, login_attempts, locked_until, created_at FROM users WHERE username = ? OR email = ?',
			[username, username]
		)

		if (rows.length === 0) {
			connection.release()
			ctx.status = 401
			ctx.body = { message: '用户名或密码错误' }
			return
		}

		const user = rows[0]

		// 检查账户状态
		if (user.status === 'banned') {
			connection.release()
			ctx.status = 403
			ctx.body = { message: '账户已被封禁' }
			return
		}

		if (user.status === 'inactive') {
			connection.release()
			ctx.status = 403
			ctx.body = { message: '账户未激活' }
			return
		}

		// 检查是否被锁定
		if (user.locked_until && new Date() < new Date(user.locked_until)) {
			connection.release()
			ctx.status = 423
			ctx.body = { message: '账户已被锁定，请稍后重试' }
			return
		}

		// 验证密码
		const isValidPassword = await bcrypt.compare(password, user.password_hash)

		if (!isValidPassword) {
			// 增加登录失败次数
			const newAttempts = user.login_attempts + 1
			let lockedUntil = null
			
			// 5次失败后锁定30分钟
			if (newAttempts >= 5) {
				lockedUntil = new Date(Date.now() + 30 * 60 * 1000)
			}

			await connection.execute(
				'UPDATE users SET login_attempts = ?, locked_until = ? WHERE id = ?',
				[newAttempts, lockedUntil, user.id]
			)
			
			connection.release()
			ctx.status = 401
			ctx.body = { message: '用户名或密码错误' }
			return
		}

		// 登录成功，重置失败次数并更新最后登录时间
		await connection.execute(
			'UPDATE users SET login_attempts = 0, locked_until = NULL, last_login = CURRENT_TIMESTAMP WHERE id = ?',
			[user.id]
		)
		
		connection.release()

		// 生成JWT token
		const token = jwt.sign(
			{ id: user.id, username: user.username, type: 'user' },
			JWT_SECRET,
			{ expiresIn: JWT_EXPIRES_IN }
		)

		ctx.body = {
			message: '登录成功',
			token,
			user: {
				id: user.id,
				username: user.username,
				email: user.email,
				nickname: user.nickname,
				phone: user.phone,
				points: user.points || 100,
				created_at: user.created_at
			}
		}

		logger.info(`🔐 用户登录成功: ${user.username}`)
	} catch (error) {
		logger.logError(error, { operation: 'user_login' })
		ctx.status = 500
		ctx.body = { message: '登录失败，请稍后重试' }
	}
})

// 用户token验证中间件
const authenticateUserToken = async (ctx, next) => {
	const authHeader = ctx.headers.authorization
	const token = authHeader && authHeader.split(' ')[1]

	if (!token) {
		ctx.status = 401
		ctx.body = { message: '缺少认证token' }
		return
	}

	try {
		const decoded = jwt.verify(token, JWT_SECRET)
		
		// 检查是否为用户token
		if (decoded.type !== 'user') {
			ctx.status = 403
			ctx.body = { message: '无效的用户token' }
			return
		}

		ctx.user = decoded
		await next()
	} catch (error) {
		logger.logError(error, { operation: 'user_token_verification' })
		ctx.status = 403
		ctx.body = { message: 'token无效或已过期' }
	}
}

// 获取用户信息
router.get('/api/users/profile', authenticateUserToken, async ctx => {
	try {
		// 设置缓存控制头，确保不使用缓存
		ctx.set('Cache-Control', 'no-cache, no-store, must-revalidate')
		ctx.set('Pragma', 'no-cache')
		ctx.set('Expires', '0')

		const connection = await pool.getConnection()
		const [rows] = await connection.execute(
			'SELECT id, username, email, nickname, avatar_url, phone, points, created_at, last_login FROM users WHERE id = ?',
			[ctx.user.id]
		)
		connection.release()

		if (rows.length === 0) {
			logger.logError(`用户不存在: ${ctx.user.id}`, { operation: 'get_user_profile' })
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		// 记录成功日志
		logger.logInfo(`用户信息获取成功: ${ctx.user.id}`, { operation: 'get_user_profile' })

		ctx.body = {
			message: '获取成功',
			user: rows[0],
			timestamp: new Date().toISOString()
		}
	} catch (error) {
		logger.logError(error, { operation: 'get_user_profile', userId: ctx.user?.id })
		ctx.status = 500
		ctx.body = { message: '获取用户信息失败', error: error.message }
	}
})

// 更新用户信息
router.put('/api/users/profile', authenticateUserToken, async ctx => {
	try {
		const { nickname, phone } = ctx.request.body
		const updateFields = {}
		const updateValues = []

		if (nickname !== undefined) {
			updateFields.nickname = '?'
			updateValues.push(nickname)
		}

		if (phone !== undefined) {
			updateFields.phone = '?'
			updateValues.push(phone)
		}

		if (updateValues.length === 0) {
			ctx.status = 400
			ctx.body = { message: '没有要更新的字段' }
			return
		}

		updateValues.push(ctx.user.id)

		const setClause = Object.keys(updateFields).map(key => `${key} = ?`).join(', ')
		
		const connection = await pool.getConnection()
		await connection.execute(
			`UPDATE users SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
			updateValues
		)
		connection.release()

		ctx.body = { message: '更新成功' }
		logger.info(`用户信息更新成功: ${ctx.user.username}`)
	} catch (error) {
		logger.logError(error, { operation: 'update_user_profile' })
		ctx.status = 500
		ctx.body = { message: '更新用户信息失败' }
	}
})

// 用户头像上传
router.post('/api/users/avatar', authenticateUserToken, upload.single('avatar'), async ctx => {
	try {
		if (!ctx.file) {
			ctx.status = 400
			ctx.body = { message: '没有上传文件' }
			return
		}

		const avatarUrl = `/uploads/icons/${ctx.file.filename}`
		
		const connection = await pool.getConnection()
		await connection.execute(
			'UPDATE users SET avatar_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
			[avatarUrl, ctx.user.id]
		)
		connection.release()

		ctx.body = {
			message: '头像上传成功',
			avatar_url: avatarUrl
		}

		logger.info(`用户头像上传成功: ${ctx.user.username}`)
	} catch (error) {
		logger.logError(error, { operation: 'upload_user_avatar' })
		ctx.status = 500
		ctx.body = { message: '头像上传失败' }
	}
})

// 检查用户积分是否足够进行对话
router.post('/api/users/check-points', authenticateUserToken, async ctx => {
	try {
		const { appId } = ctx.request.body

		if (!appId) {
			ctx.status = 400
			ctx.body = { message: '应用ID不能为空' }
			return
		}

		const connection = await pool.getConnection()
		
		// 获取用户当前积分
		const [userRows] = await connection.execute(
			'SELECT points FROM users WHERE id = ?',
			[ctx.user.id]
		)

		if (userRows.length === 0) {
			connection.release()
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		const userPoints = userRows[0].points || 0

		// 获取应用消耗积分
		const [appRows] = await connection.execute('SELECT app_data FROM apps WHERE id = ?', [appId])
		
		if (appRows.length === 0) {
			connection.release()
			ctx.status = 404
			ctx.body = { message: '应用不存在' }
			return
		}

		let appPointsCost = 0
		try {
			const appData = JSON.parse(appRows[0].app_data)
			appPointsCost = appData.info?.points_cost || 0
		} catch (error) {
			logger.logError(error, { operation: 'parse_app_data_check_points' })
		}

		connection.release()

		const canChat = userPoints >= appPointsCost

		ctx.body = {
			success: true,
			data: {
				userPoints,
				requiredPoints: appPointsCost,
				canChat,
				message: canChat ? '积分充足' : '积分不足，请联系管理员充值'
			}
		}

		logger.info(`用户积分检查: ${ctx.user.username} (${userPoints}分) 应用需要 ${appPointsCost}分`)
	} catch (error) {
		logger.logError(error, { operation: 'check_user_points' })
		ctx.status = 500
		ctx.body = { message: '检查积分失败' }
	}
})

// 消耗用户积分（用于对话）
router.post('/api/users/consume-points', authenticateUserToken, async ctx => {
	try {
		const { appId } = ctx.request.body

		if (!appId) {
			ctx.status = 400
			ctx.body = { message: '应用ID不能为空' }
			return
		}

		const connection = await pool.getConnection()
		
		// 开始事务
		await connection.beginTransaction()

		try {
			// 获取用户当前积分（加锁）
			const [userRows] = await connection.execute(
				'SELECT points FROM users WHERE id = ? FOR UPDATE',
				[ctx.user.id]
			)

			if (userRows.length === 0) {
				await connection.rollback()
				connection.release()
				ctx.status = 404
				ctx.body = { message: '用户不存在' }
				return
			}

			const currentPoints = userRows[0].points || 0

			// 获取应用消耗积分
			const [appRows] = await connection.execute('SELECT app_data FROM apps WHERE id = ?', [appId])
			
			if (appRows.length === 0) {
				await connection.rollback()
				connection.release()
				ctx.status = 404
				ctx.body = { message: '应用不存在' }
				return
			}

			let appPointsCost = 0
			try {
				const appData = JSON.parse(appRows[0].app_data)
				appPointsCost = appData.info?.points_cost || 0
			} catch (error) {
				logger.logError(error, { operation: 'parse_app_data_consume_points' })
			}

			// 如果应用不消耗积分，直接返回成功
			if (appPointsCost === 0) {
				await connection.commit()
				connection.release()
				ctx.body = {
					success: true,
					data: {
						consumed: 0,
						remainingPoints: currentPoints,
						message: '此应用无需消耗积分'
					}
				}
				return
			}

			// 检查积分是否足够
			if (currentPoints < appPointsCost) {
				await connection.rollback()
				connection.release()
				ctx.status = 400
				ctx.body = { 
					success: false,
					message: '积分不足，请联系管理员充值',
					data: {
						currentPoints,
						requiredPoints: appPointsCost
					}
				}
				return
			}

			// 扣除积分
			const newPoints = currentPoints - appPointsCost
			await connection.execute(
				'UPDATE users SET points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
				[newPoints, ctx.user.id]
			)

			await connection.commit()
			connection.release()

			ctx.body = {
				success: true,
				data: {
					consumed: appPointsCost,
					remainingPoints: newPoints,
					message: `成功消耗 ${appPointsCost} 积分`
				}
			}

			logger.info(`用户积分消耗: ${ctx.user.username} 消耗 ${appPointsCost}分，余额 ${newPoints}分`)
		} catch (error) {
			await connection.rollback()
			connection.release()
			throw error
		}
	} catch (error) {
		logger.logError(error, { operation: 'consume_user_points' })
		ctx.status = 500
		ctx.body = { message: '消耗积分失败' }
	}
})

// ==================== 认证相关API ====================

// 管理员登录
router.post('/admin/login', async ctx => {
	try {
		const { username, password } = ctx.request.body

		if (!username || !password) {
			ctx.status = 400
			ctx.body = { message: '用户名和密码不能为空' }
			return
		}

		const connection = await pool.getConnection()
		const [rows] = await connection.execute(
			'SELECT id, username, password_hash FROM admins WHERE username = ?',
			[username]
		)
		connection.release()

		if (rows.length === 0) {
			ctx.status = 401
			ctx.body = { message: '用户名或密码错误' }
			return
		}

		const admin = rows[0]
		const isValidPassword = await bcrypt.compare(password, admin.password_hash)

		if (!isValidPassword) {
			ctx.status = 401
			ctx.body = { message: '用户名或密码错误' }
			return
		}

		// 生成JWT token
		const token = jwt.sign(
			{ id: admin.id, username: admin.username },
			JWT_SECRET,
			{ expiresIn: JWT_EXPIRES_IN }
		)

		ctx.body = {
			message: '登录成功',
			token,
			user: {
				id: admin.id,
				username: admin.username
			}
		}

		console.log(`🔐 管理员登录成功: ${username}`)
	} catch (error) {
		console.error('管理员登录失败:', error)
		ctx.status = 500
		ctx.body = { message: '登录失败', error: error.message }
	}
})

// 验证token有效性
router.get('/admin/verify', authenticateToken, async ctx => {
	ctx.body = {
		message: 'token有效',
		user: ctx.user
	}
})

// ==================== 辅助函数 ====================

/**
 * 从数据库获取并格式化系统设置
 * @param {object} options - 选项
 * @param {boolean} [options.isPublic=false] - 是否只获取公开设置并简化格式
 * @returns {Promise<object>}
 */
const fetchAndFormatSettings = async ({ isPublic = false } = {}) => {
	const connection = await pool.getConnection()
	let rows

	try {
		if (isPublic) {
			const publicSettingKeys = ['default_theme', 'site_title', 'favicon_frontend', 'logo_frontend']
			;[rows] = await connection.query(
				`SELECT setting_key, setting_value, setting_type FROM system_settings WHERE setting_key IN (?)`,
				[publicSettingKeys] // 注意: 将数组参数包裹在另一个数组中
			)
		} else {
			[rows] = await connection.execute('SELECT * FROM system_settings ORDER BY setting_key')
		}
	} finally {
		connection.release()
	}

	const settings = {}
	rows.forEach(row => {
		let value = row.setting_value
		// 根据类型转换值
		switch (row.setting_type) {
			case 'number':
				value = Number(value)
				break
			case 'boolean':
				value = value === 'true'
				break
			case 'json':
				try {
					value = JSON.parse(value)
				} catch (e) {
					// 解析失败则保持字符串
					value = row.setting_value
				}
				break
			default:
				// string 类型保持原样
				break
		}

		if (isPublic) {
			settings[row.setting_key] = value
		} else {
			settings[row.setting_key] = {
				value: value,
				type: row.setting_type,
				description: row.description
			}
		}
	})

	return settings
}

// ==================== 管理员系统管理API ====================

// 获取系统设置
router.get('/admin/settings', authenticateToken, async ctx => {
	try {
		const settings = await fetchAndFormatSettings({ isPublic: false })
		ctx.body = {
			success: true,
			data: settings
		}
		logger.info('管理员获取系统设置成功')
	} catch (error) {
		logger.logError(error, { operation: 'get_system_settings' })
		ctx.status = 500
		ctx.body = { message: '获取系统设置失败' }
	}
})

// 更新系统设置
router.put('/admin/settings', authenticateToken, async ctx => {
	try {
		const settings = ctx.request.body
		if (!settings || typeof settings !== 'object') {
			ctx.status = 400
			ctx.body = { message: '设置数据格式错误' }
			return
		}

		const connection = await pool.getConnection()
		
		// 开启事务
		await connection.beginTransaction()

		try {
			for (const [key, settingData] of Object.entries(settings)) {
				const { value, type } = settingData
				let stringValue = value

				// 根据类型转换为字符串存储
				if (type === 'json') {
					stringValue = JSON.stringify(value)
				} else {
					stringValue = String(value)
				}

				await connection.execute(`
					UPDATE system_settings 
					SET setting_value = ?, updated_at = CURRENT_TIMESTAMP 
					WHERE setting_key = ?
				`, [stringValue, key])
			}

			// 提交事务
			await connection.commit()
			logger.debug('数据库事务提交成功', { operation: 'update_settings' })

			// 清除缓存
			await cache.del('system_settings:public')
			await cache.del('system_settings:all')
			logger.info('系统设置缓存已清除')

			ctx.body = {
				success: true,
				message: '设置更新成功'
			}
		} catch (error) {
			await connection.rollback()
			throw error
		}
	} catch (error) {
		logger.logError(error, { operation: 'update_system_settings' })
		ctx.status = 500
		ctx.body = { message: '更新系统设置失败' }
	}
})

// 图标上传接口
router.post('/admin/upload/icon', authenticateToken, upload.single('icon'), async ctx => {
	try {
		if (!ctx.file) {
			ctx.status = 400
			ctx.body = { message: '没有上传文件' }
			return
		}

		// 返回文件相对路径
		const relativePath = `/uploads/icons/${ctx.file.filename}`
		
		ctx.body = {
			success: true,
			message: '图标上传成功',
			data: {
				filename: ctx.file.filename,
				path: relativePath,
				originalname: ctx.file.originalname,
				size: ctx.file.size
			}
		}
		
		logger.info(`管理员上传图标成功: ${ctx.file.originalname} -> ${ctx.file.filename}`)
	} catch (error) {
		logger.logError(error, { operation: 'upload_icon' })
		ctx.status = 500
		ctx.body = { message: error.message || '图标上传失败' }
	}
})

// 缓存管理接口
router.get('/admin/cache/stats', authenticateToken, async ctx => {
	try {
		const stats = cache.getStats()
		ctx.body = {
			success: true,
			data: stats
		}
	} catch (error) {
		logger.logError(error, { operation: 'get_cache_stats' })
		ctx.status = 500
		ctx.body = { message: '获取缓存统计失败' }
	}
})

// 清空缓存
router.delete('/admin/cache', authenticateToken, async ctx => {
	try {
		await cache.flush()
		logger.info('管理员清空所有缓存')
		ctx.body = {
			success: true,
			message: '缓存已清空'
		}
	} catch (error) {
		logger.logError(error, { operation: 'flush_cache' })
		ctx.status = 500
		ctx.body = { message: '清空缓存失败' }
	}
})

// 删除指定缓存
router.delete('/admin/cache/:key', authenticateToken, async ctx => {
	try {
		const key = ctx.params.key
		await cache.del(key)
		logger.info(`管理员删除缓存: ${key}`)
		ctx.body = {
			success: true,
			message: `缓存 ${key} 已删除`
		}
	} catch (error) {
		logger.logError(error, { operation: 'delete_cache_key', key: ctx.params.key })
		ctx.status = 500
		ctx.body = { message: '删除缓存失败' }
	}
})

// 调度器管理接口
router.get('/admin/scheduler/status', authenticateToken, async ctx => {
	try {
		const taskInfo = scheduler.getTaskInfo()
		ctx.body = {
			success: true,
			data: {
				running: scheduler.isRunning,
				tasks: taskInfo
			}
		}
	} catch (error) {
		logger.logError(error, { operation: 'get_scheduler_status' })
		ctx.status = 500
		ctx.body = { message: '获取调度器状态失败' }
	}
})

// 手动执行任务
router.post('/admin/scheduler/run/:taskName', authenticateToken, async ctx => {
	try {
		const taskName = ctx.params.taskName
		const result = await scheduler.runTaskNow(taskName)
		logger.info(`管理员手动执行任务: ${taskName}`)
		ctx.body = {
			success: true,
			message: `任务 ${taskName} 执行完成`,
			data: result
		}
	} catch (error) {
		logger.logError(error, { operation: 'run_task_manual', taskName: ctx.params.taskName })
		ctx.status = 500
		ctx.body = { message: error.message }
	}
})

// ==================== 管理员应用管理API ====================

// 管理员获取所有应用
router.get('/admin/apps', authenticateToken, async ctx => {
	const startTime = Date.now()
	try {
		// 管理员查看也使用缓存以提升性能
		const cacheKey = 'apps_list'
		const cachedApps = await cache.get(cacheKey)
		
		if (cachedApps) {
			logger.info('管理员应用列表缓存命中')
			ctx.body = cachedApps
			return
		}

		const connection = await pool.getConnection()
		const [rows] = await connection.execute('SELECT * FROM apps ORDER BY created_at DESC')
		connection.release()

		const apps = rows.map(row => {
			try {
				const app = JSON.parse(row.app_data)
				// 兼容处理新字段，确保前端需要的字段都存在
				if (!app.info) app.info = {};
				app.info.tags = app.info.tags || []
				app.info.points_cost = app.info.points_cost || 0

				// 确保 requestConfig 字段存在
				if (!app.requestConfig) {
					app.requestConfig = {
						apiBase: app.apiBase || 'https://api.dify.ai/v1',
						apiKey: app.apiKey || 'app-placeholder'
					}
				}

				// 确保其他必需字段存在
				if (!app.answerForm) {
					app.answerForm = {
						enabled: false,
						feedbackText: ''
					}
				}

				if (!app.inputParams) {
					app.inputParams = {
						enableUpdateAfterCvstStarts: false
					}
				}

				if (!app.extConfig) {
					app.extConfig = {
						conversation: {
							openingStatement: {
								displayMode: 'default'
							}
						}
					}
				}

				return app
			} catch (error) {
				logger.logError(error, { operation: 'parse_app_data_admin', rowId: row.id })
				return null
			}
		}).filter(app => app !== null)

		// 缓存结果
		await cache.set(cacheKey, apps, config.cache.appListTTL)

		ctx.body = apps
		logger.logDatabaseOperation('SELECT', 'apps', Date.now() - startTime)
		logger.info(`管理员获取应用列表成功，共 ${apps.length} 个应用`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_get_apps' })
		logger.logDatabaseOperation('SELECT', 'apps', Date.now() - startTime, false)
		ctx.status = 500
		ctx.body = { message: '获取应用列表失败' }
	}
})

// 管理员新增应用
router.post('/admin/apps', authenticateToken, async ctx => {
	try {
		const newApp = ctx.request.body

		// 确保新字段有默认值
		if (!newApp.info) newApp.info = {};
		newApp.info.tags = newApp.info.tags || []
		newApp.info.points_cost = newApp.info.points_cost || 0

		const connection = await pool.getConnection()
		
		const appDataJson = JSON.stringify(newApp)
		
		await connection.execute(
			'INSERT INTO apps (id, app_data) VALUES (?, ?)',
			[newApp.id, appDataJson]
		)

		connection.release()
		
		// 清理相关缓存
		await cache.del('apps_list')
		await cache.del(`app_detail_${newApp.id}`)
		
		ctx.status = 201
		ctx.body = newApp
		logger.info(`管理员添加应用成功: ${newApp.info?.name || newApp.id}`)
	} catch (error) {
		console.error('添加应用失败:', error)
		if (error.code === 'ER_DUP_ENTRY') {
			ctx.status = 409
			ctx.body = { message: '应用ID已存在' }
		} else {
			ctx.status = 500
			ctx.body = { message: '添加应用失败', error: error.message }
		}
	}
})

// 管理员更新应用
router.put('/admin/apps/:id', authenticateToken, async ctx => {
	try {
		const id = ctx.params.id
		const updatedApp = ctx.request.body

		// 确保新字段有默认值
		if (!updatedApp.info) updatedApp.info = {};
		updatedApp.info.tags = updatedApp.info.tags || []
		updatedApp.info.points_cost = updatedApp.info.points_cost || 0
		
		const connection = await pool.getConnection()
		
		const appDataJson = JSON.stringify(updatedApp)
		
		const [result] = await connection.execute(
			'UPDATE apps SET app_data = ? WHERE id = ?',
			[appDataJson, id]
		)

		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: 'App not found' }
		} else {
			// 清理相关缓存
			await cache.del('apps_list')
			await cache.del(`app_detail_${id}`)
			
			ctx.body = updatedApp
			logger.info(`管理员更新应用成功: ${updatedApp.info?.name || id}`)
		}
	} catch (error) {
		console.error('更新应用失败:', error)
		ctx.status = 500
		ctx.body = { message: '更新应用失败', error: error.message }
	}
})

// 管理员删除应用
router.delete('/admin/apps/:id', authenticateToken, async ctx => {
	try {
		const id = ctx.params.id
		const connection = await pool.getConnection()
		
		const [result] = await connection.execute('DELETE FROM apps WHERE id = ?', [id])
		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: 'App not found' }
		} else {
			// 清理相关缓存
			await cache.del('apps_list')
			await cache.del(`app_detail_${id}`)
			
			ctx.status = 204
			logger.info(`管理员删除应用成功: ${id}`)
		}
	} catch (error) {
		console.error('删除应用失败:', error)
		ctx.status = 500
		ctx.body = { message: '删除应用失败', error: error.message }
	}
})

// ==================== 管理员用户管理API ====================

// 获取所有用户
router.get('/admin/users', authenticateToken, async ctx => {
	try {
		const { page = 1, limit = 20, status, search } = ctx.query
		const offset = (page - 1) * limit

		let whereClause = '1=1'
		const queryParams = []

		// 状态筛选
		if (status && ['active', 'inactive', 'banned'].includes(status)) {
			whereClause += ' AND status = ?'
			queryParams.push(status)
		}

		// 搜索条件
		if (search) {
			whereClause += ' AND (username LIKE ? OR email LIKE ? OR nickname LIKE ?)'
			const searchPattern = `%${search}%`
			queryParams.push(searchPattern, searchPattern, searchPattern)
		}

		const connection = await pool.getConnection()

		// 获取总数
		const [countResult] = await connection.execute(
			`SELECT COUNT(*) as total FROM users WHERE ${whereClause}`,
			queryParams
		)
		const total = countResult[0].total

		// 获取用户列表
		const [rows] = await connection.execute(
			`SELECT id, username, email, nickname, phone, points, status, last_login, created_at
			 FROM users
			 WHERE ${whereClause}
			 ORDER BY created_at DESC
			 LIMIT ? OFFSET ?`,
			[...queryParams, parseInt(limit), parseInt(offset)]
		)

		connection.release()

		ctx.body = {
			success: true,
			data: {
				users: rows,
				total,
				page: parseInt(page),
				limit: parseInt(limit),
				totalPages: Math.ceil(total / limit)
			}
		}

		logger.info(`管理员获取用户列表成功，共 ${rows.length} 个用户`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_get_users' })
		ctx.status = 500
		ctx.body = { message: '获取用户列表失败' }
	}
})

// 获取单个用户详情
router.get('/admin/users/:id', authenticateToken, async ctx => {
	try {
		const userId = ctx.params.id

		const connection = await pool.getConnection()
		const [rows] = await connection.execute(
			'SELECT id, username, email, nickname, avatar_url, phone, points, status, login_attempts, locked_until, last_login, created_at, updated_at FROM users WHERE id = ?',
			[userId]
		)
		connection.release()

		if (rows.length === 0) {
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		ctx.body = {
			success: true,
			data: rows[0]
		}

		logger.info(`管理员获取用户详情成功: ${rows[0].username}`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_get_user_detail' })
		ctx.status = 500
		ctx.body = { message: '获取用户详情失败' }
	}
})

// 更新用户状态
router.put('/admin/users/:id/status', authenticateToken, async ctx => {
	try {
		const userId = ctx.params.id
		const { status } = ctx.request.body

		if (!['active', 'inactive', 'banned'].includes(status)) {
			ctx.status = 400
			ctx.body = { message: '无效的状态值' }
			return
		}

		const connection = await pool.getConnection()
		const [result] = await connection.execute(
			'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
			[status, userId]
		)
		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		ctx.body = { message: '用户状态更新成功' }
		logger.info(`管理员更新用户状态成功: 用户ID ${userId} -> ${status}`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_update_user_status' })
		ctx.status = 500
		ctx.body = { message: '更新用户状态失败' }
	}
})

// 重置用户密码
router.put('/admin/users/:id/password', authenticateToken, async ctx => {
	try {
		const userId = ctx.params.id
		const { newPassword } = ctx.request.body

		if (!newPassword || newPassword.length < 6) {
			ctx.status = 400
			ctx.body = { message: '新密码长度不能少于6个字符' }
			return
		}

		const passwordHash = await bcrypt.hash(newPassword, 10)

		const connection = await pool.getConnection()
		const [result] = await connection.execute(
			'UPDATE users SET password_hash = ?, login_attempts = 0, locked_until = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
			[passwordHash, userId]
		)
		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		ctx.body = { message: '密码重置成功' }
		logger.info(`管理员重置用户密码成功: 用户ID ${userId}`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_reset_user_password' })
		ctx.status = 500
		ctx.body = { message: '重置密码失败' }
	}
})

// 解锁用户账户
router.put('/admin/users/:id/unlock', authenticateToken, async ctx => {
	try {
		const userId = ctx.params.id

		const connection = await pool.getConnection()
		const [result] = await connection.execute(
			'UPDATE users SET login_attempts = 0, locked_until = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
			[userId]
		)
		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		ctx.body = { message: '用户账户解锁成功' }
		logger.info(`管理员解锁用户账户成功: 用户ID ${userId}`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_unlock_user' })
		ctx.status = 500
		ctx.body = { message: '解锁用户账户失败' }
	}
})

// 删除用户
router.delete('/admin/users/:id', authenticateToken, async ctx => {
	try {
		const userId = ctx.params.id

		const connection = await pool.getConnection()
		const [result] = await connection.execute('DELETE FROM users WHERE id = ?', [userId])
		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		ctx.status = 204
		logger.info(`管理员删除用户成功: 用户ID ${userId}`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_delete_user' })
		ctx.status = 500
		ctx.body = { message: '删除用户失败' }
	}
})

// 管理用户积分
router.put('/admin/users/:id/points', authenticateToken, async ctx => {
	try {
		const userId = ctx.params.id
		const { action, amount, reason } = ctx.request.body

		if (!['add', 'subtract', 'set'].includes(action)) {
			ctx.status = 400
			ctx.body = { message: '无效的操作类型，支持: add, subtract, set' }
			return
		}

		if (!amount || amount <= 0) {
			ctx.status = 400
			ctx.body = { message: '积分数量必须大于0' }
			return
		}

		const connection = await pool.getConnection()

		// 先获取当前用户信息
		const [userRows] = await connection.execute(
			'SELECT username, points FROM users WHERE id = ?',
			[userId]
		)

		if (userRows.length === 0) {
			connection.release()
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		const user = userRows[0]
		let newPoints = user.points

		// 根据操作类型计算新积分
		switch (action) {
			case 'add':
				newPoints += parseInt(amount)
				break
			case 'subtract':
				newPoints = Math.max(0, newPoints - parseInt(amount)) // 不能小于0
				break
			case 'set':
				newPoints = Math.max(0, parseInt(amount))
				break
		}

		// 更新积分
		const [result] = await connection.execute(
			'UPDATE users SET points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
			[newPoints, userId]
		)

		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: '用户不存在' }
			return
		}

		ctx.body = { 
			message: '积分更新成功',
			data: {
				oldPoints: user.points,
				newPoints: newPoints,
				change: newPoints - user.points
			}
		}
		
		logger.info(`管理员更新用户积分: ${user.username} (${user.points} -> ${newPoints}) 原因: ${reason || '无'}`)
	} catch (error) {
		logger.logError(error, { operation: 'admin_update_user_points' })
		ctx.status = 500
		ctx.body = { message: '更新用户积分失败' }
	}
})

// ==================== 公开API ====================

// 获取公开的系统设置
router.get('/settings', async ctx => {
	const startTime = Date.now()
	try {
		// 尝试从缓存获取
		const cacheKey = 'public_system_settings'
		const cachedSettings = await cache.get(cacheKey)
		
		if (cachedSettings) {
			logger.info('公开系统设置缓存命中')
			ctx.body = cachedSettings
			return
		}

		const settings = await fetchAndFormatSettings({ isPublic: true })

		const result = {
			success: true,
			data: settings
		}

		// 缓存结果
		await cache.set(cacheKey, result, config.cache.systemSettingsTTL || 300) // 5分钟缓存

		ctx.body = result
		logger.logDatabaseOperation('SELECT', 'system_settings', Date.now() - startTime)
		logger.info('获取公开系统设置成功')
	} catch (error) {
		logger.logError(error, { operation: 'get_public_settings' })
		logger.logDatabaseOperation('SELECT', 'system_settings', Date.now() - startTime, false)
		ctx.status = 500
		ctx.body = { message: '获取系统设置失败' }
	}
})

// 获取所有应用
router.get('/apps', async ctx => {
	const startTime = Date.now()
	try {
		// 尝试从缓存获取
		const cacheKey = 'apps_list'
		const cachedApps = await cache.get(cacheKey)
		
		if (cachedApps) {
			logger.info('应用列表缓存命中')
			ctx.body = cachedApps
			return
		}

		const connection = await pool.getConnection()
		const [rows] = await connection.execute('SELECT * FROM apps ORDER BY created_at DESC')
		connection.release()

		// 将数据库格式转换为前端需要的格式
		const apps = rows.map(row => {
			try {
				const app = JSON.parse(row.app_data)
				// 兼容处理新字段
				if (!app.info) app.info = {};
				app.info.tags = app.info.tags || []
				app.info.points_cost = app.info.points_cost || 0
				return app
			} catch (error) {
				logger.logError(error, { operation: 'parse_app_data', rowId: row.id })
				return null
			}
		}).filter(app => app !== null)

		// 缓存结果
		await cache.set(cacheKey, apps, config.cache.appListTTL)

		ctx.body = apps
		logger.logDatabaseOperation('SELECT', 'apps', Date.now() - startTime)
		logger.info(`应用列表获取成功，共 ${apps.length} 个应用`)
	} catch (error) {
		logger.logError(error, { operation: 'get_apps' })
		logger.logDatabaseOperation('SELECT', 'apps', Date.now() - startTime, false)
		ctx.status = 500
		ctx.body = { message: '获取应用列表失败' }
	}
})

// 根据 ID 获取应用
router.get('/apps/:id', async ctx => {
	const startTime = Date.now()
	try {
		const id = ctx.params.id
		
		// 尝试从缓存获取
		const cacheKey = `app_detail_${id}`
		const cachedApp = await cache.get(cacheKey)
		
		if (cachedApp) {
			logger.info('应用详情缓存命中', { appId: id })
			ctx.body = cachedApp
			return
		}

		const connection = await pool.getConnection()
		const [rows] = await connection.execute('SELECT * FROM apps WHERE id = ?', [id])
		connection.release()

		if (rows.length === 0) {
			ctx.status = 404
			ctx.body = { message: 'App not found' }
			return
		}

		const app = JSON.parse(rows[0].app_data)
		
		// 兼容处理新字段
		if (!app.info) app.info = {};
		app.info.tags = app.info.tags || []
		app.info.points_cost = app.info.points_cost || 0

		// 缓存结果
		await cache.set(cacheKey, app, config.cache.appDetailTTL)

		ctx.body = app
		logger.logDatabaseOperation('SELECT', 'apps', Date.now() - startTime)
		logger.info(`应用详情获取成功: ${app.info?.name || id}`)
	} catch (error) {
		logger.logError(error, { operation: 'get_app_detail', appId: ctx.params.id })
		logger.logDatabaseOperation('SELECT', 'apps', Date.now() - startTime, false)
		ctx.status = 500
		ctx.body = { message: '获取应用详情失败' }
	}
})

// 新增应用
router.post('/apps', async ctx => {
	try {
		const newApp = ctx.request.body
		// 确保新字段有默认值
		if (!newApp.info) newApp.info = {};
		newApp.info.tags = newApp.info.tags || []
		newApp.info.points_cost = newApp.info.points_cost || 0

		const connection = await pool.getConnection()
		
		// 将整个应用配置对象转换为JSON字符串存储
		const appDataJson = JSON.stringify(newApp)
		
		await connection.execute(
			'INSERT INTO apps (id, app_data) VALUES (?, ?)',
			[newApp.id, appDataJson]
		)

		connection.release()
		
		// 清理相关缓存
		await cache.del('apps_list')
		await cache.del(`app_detail_${newApp.id}`)
		
		ctx.status = 201
		ctx.body = newApp
		logger.info(`应用添加成功: ${newApp.info?.name || newApp.id}`)
	} catch (error) {
		console.error('添加应用失败:', error)
		if (error.code === 'ER_DUP_ENTRY') {
			ctx.status = 409
			ctx.body = { message: '应用ID已存在' }
		} else {
			ctx.status = 500
			ctx.body = { message: '添加应用失败', error: error.message }
		}
	}
})

// 更新应用
router.put('/apps/:id', async ctx => {
	try {
		const id = ctx.params.id
		const updatedApp = ctx.request.body
		// 确保新字段有默认值
		if (!updatedApp.info) updatedApp.info = {};
		updatedApp.info.tags = updatedApp.info.tags || []
		updatedApp.info.points_cost = updatedApp.info.points_cost || 0

		const connection = await pool.getConnection()
		
		// 将整个应用配置对象转换为JSON字符串存储
		const appDataJson = JSON.stringify(updatedApp)
		
		const [result] = await connection.execute(
			'UPDATE apps SET app_data = ? WHERE id = ?',
			[appDataJson, id]
		)

		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: 'App not found' }
		} else {
			// 清理相关缓存
			await cache.del('apps_list')
			await cache.del(`app_detail_${id}`)
			
			ctx.body = updatedApp
			logger.info(`应用更新成功: ${updatedApp.info?.name || id}`)
		}
	} catch (error) {
		console.error('更新应用失败:', error)
		ctx.status = 500
		ctx.body = { message: '更新应用失败', error: error.message }
	}
})

// 删除应用
router.delete('/apps/:id', async ctx => {
	try {
		const id = ctx.params.id
		const connection = await pool.getConnection()
		
		const [result] = await connection.execute('DELETE FROM apps WHERE id = ?', [id])
		connection.release()

		if (result.affectedRows === 0) {
			ctx.status = 404
			ctx.body = { message: 'App not found' }
		} else {
			// 清理相关缓存
			await cache.del('apps_list')
			await cache.del(`app_detail_${id}`)
			
			ctx.status = 204
			logger.info(`应用删除成功: ${id}`)
		}
	} catch (error) {
		console.error('删除应用失败:', error)
		ctx.status = 500
		ctx.body = { message: '删除应用失败', error: error.message }
	}
})

// 健康检查接口
router.get('/health', async ctx => {
	const startTime = Date.now()
	let dbStatus = 'unknown'
	let dbError = null
	
	try {
		// 尝试从缓存获取健康检查结果
		const cacheKey = 'health_check'
		const cachedHealth = await cache.get(cacheKey)
		
		if (cachedHealth) {
			ctx.body = {
				...cachedHealth,
				cached: true,
				timestamp: new Date().toISOString()
			}
			return
		}

		const connection = await pool.getConnection()
		await connection.execute('SELECT 1')
		connection.release()
		dbStatus = 'connected'
		
		const stats = healthMonitor.getStats()
		const dbDuration = Date.now() - startTime
		
		// 获取缓存和调度器状态
		const cacheStats = cache.getStats()
		const schedulerTasks = scheduler.getTaskInfo()
		
		// 获取系统统计信息
		const systemStats = await cache.get('system_stats')
		
		const healthData = {
			status: 'healthy',
			timestamp: new Date().toISOString(),
			database: {
				status: dbStatus,
				responseTime: `${dbDuration}ms`
			},
			server: {
				uptime: stats.uptime,
				requests: stats.requestCount,
				errors: stats.errorCount,
				errorRate: stats.errorRate,
				memory: {
					used: Math.round(stats.memory.heapUsed / 1024 / 1024) + 'MB',
					total: Math.round(stats.memory.heapTotal / 1024 / 1024) + 'MB'
				},
				pid: stats.pid,
				nodeVersion: stats.version,
				environment: stats.environment
			},
			cache: cacheStats,
			scheduler: {
				running: scheduler.isRunning,
				tasks: schedulerTasks
			},
			systemStats: systemStats || null
		}
		
		// 缓存健康检查结果
		await cache.set(cacheKey, healthData, config.cache.healthCheckTTL)
		
		ctx.body = healthData
		logger.logDatabaseOperation('HEALTH_CHECK', 'health', dbDuration)
	} catch (error) {
		dbStatus = 'disconnected'
		dbError = error.message
		
		logger.logError(error, { operation: 'health_check' })
		logger.logDatabaseOperation('HEALTH_CHECK', 'health', Date.now() - startTime, false)
		
		ctx.status = 500
		ctx.body = {
			status: 'unhealthy',
			timestamp: new Date().toISOString(),
			database: {
				status: dbStatus,
				error: dbError
			},
			server: healthMonitor.getStats()
		}
	}
})

// CORS处理 - 必须在路由之前
app.use(async (ctx, next) => {
	ctx.set('Access-Control-Allow-Origin', '*')
	ctx.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
	ctx.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept')
	ctx.set('Access-Control-Allow-Credentials', 'true')
	
	if (ctx.method === 'OPTIONS') {
		ctx.status = 204
	} else {
		await next()
	}
})

// 健康监控中间件
app.use(async (ctx, next) => {
	healthMonitor.incrementRequest()
	try {
		await next()
	} catch (error) {
		healthMonitor.incrementError()
		throw error
	}
})

// 注册API路由
app.use(router.routes())
app.use(router.allowedMethods())

// 静态文件服务 - 用于访问上传的图标（必须在路由之后）
app.use(async (ctx, next) => {
	if (ctx.path.startsWith('/uploads/')) {
		// 移除 /uploads 前缀，直接访问文件
		const filePath = path.join(__dirname, ctx.path)
		const fs = require('fs')
		
		try {
			const stats = await fs.promises.stat(filePath)
			if (stats.isFile()) {
				ctx.type = path.extname(filePath)
				ctx.body = fs.createReadStream(filePath)
				return
			}
		} catch (err) {
			// 文件不存在，继续到下一个中间件
		}
	}
	await next()
})

// 健康监控
const healthMonitor = {
	startTime: Date.now(),
	requestCount: 0,
	errorCount: 0,
	
	getStats() {
		const uptime = Date.now() - this.startTime
		return {
			uptime: Math.floor(uptime / 1000), // 秒
			requestCount: this.requestCount,
			errorCount: this.errorCount,
			errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount * 100).toFixed(2) + '%' : '0%',
			memory: process.memoryUsage(),
			pid: process.pid,
			version: process.version,
			environment: config.NODE_ENV
		}
	},
	
	incrementRequest() {
		this.requestCount++
	},
	
	incrementError() {
		this.errorCount++
	}
}

// 优雅关闭处理
const gracefulShutdown = async (signal) => {
	logger.info(`收到${signal}信号，开始优雅关闭服务器...`)
	
	try {
		// 清理健康检查定时器
		if (global.healthCheckTimer) {
			clearInterval(global.healthCheckTimer)
			logger.info('健康检查定时器已清理')
		}

		// 关闭HTTP服务器
		if (server) {
			await new Promise((resolve) => {
				server.close(resolve)
			})
			logger.info('HTTP服务器已关闭')
		}

		// 停止任务调度器
		await scheduler.stop()

		// 关闭缓存连接
		await cache.disconnect()

		// 关闭数据库连接池
		if (pool) {
			await pool.end()
			logger.info('数据库连接池已关闭')
		}
		
		logger.info('服务器优雅关闭完成')
		process.exit(0)
	} catch (error) {
		logger.logError(error, { operation: 'graceful_shutdown' })
		process.exit(1)
	}
}

// 注册优雅关闭事件
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'))
process.on('SIGINT', () => gracefulShutdown('SIGINT'))

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
	logger.logError(error, { operation: 'uncaught_exception' })
	gracefulShutdown('uncaughtException')
})

process.on('unhandledRejection', (reason, promise) => {
	logger.logError(new Error(`Unhandled Rejection: ${reason}`), { 
		operation: 'unhandled_rejection',
		promise: promise.toString()
	})
})

const port = sharedConfig.ports.server

// 启动服务器前先进行系统初始化
const startServer = async () => {
	try {
		// 先进行系统初始化
		const initSuccess = await initializeSystem()
		if (!initSuccess) {
			logger.logError(new Error('系统初始化失败'), { operation: 'server_startup' })
			process.exit(1)
		}
		
		// 启动HTTP服务器 - 监听所有网络接口
		const host = process.env.SERVER_HOST || '0.0.0.0' // 默认监听所有接口
		const server = app.listen(port, host, () => {
			logger.info('Autobot API 服务器启动成功', {
				host,
				port,
				environment: config.NODE_ENV,
				database: `${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`,
				healthCheck: `http://${host === '0.0.0.0' ? 'localhost' : host}:${port}/health`,
				pid: process.pid
			})

			// 启动健康检查定时器（保存引用以便清理）
			global.healthCheckTimer = setInterval(() => {
				try {
					const stats = healthMonitor.getStats()
					logger.debug('健康检查', stats)
				} catch (error) {
					logger.logError(error, { operation: 'health_check_timer' })
				}
			}, config.monitoring.healthCheckInterval)
		})
		
		return server
	} catch (error) {
		logger.logError(error, { operation: 'server_startup' })
		process.exit(1)
	}
}

// 启动服务器
const server = startServer()
