interface LoginRequest {
	username: string
	password: string
}

interface LoginResponse {
	message: string
	token: string
	user: {
		id: number
		username: string
	}
}

interface VerifyResponse {
	message: string
	user: {
		id: number
		username: string
	}
}

class AuthService {
	private static readonly TOKEN_KEY = 'admin_token'
	private static readonly USER_KEY = 'admin_user'

	/**
	 * 管理员登录
	 */
	static async login(credentials: LoginRequest): Promise<LoginResponse> {
		try {
			const response = await fetch('/admin/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(credentials),
			})

			if (!response.ok) {
				let errorMessage = '登录失败'
				try {
					const error = await response.json()
					errorMessage = error.message || '登录失败'
				} catch (parseError) {
					// 如果无法解析错误响应，使用默认错误信息
					console.error('解析错误响应失败:', parseError)
					if (response.status === 401) {
						errorMessage = '用户名或密码错误'
					} else if (response.status === 429) {
						errorMessage = '登录尝试过于频繁，请稍后再试'
					} else {
						errorMessage = `登录失败 (${response.status})`
					}
				}
				throw new Error(errorMessage)
			}

			const data: LoginResponse = await response.json()

			// 保存token和用户信息到localStorage
			localStorage.setItem(this.TOKEN_KEY, data.token)
			localStorage.setItem(this.USER_KEY, JSON.stringify(data.user))

			return data
		} catch (error) {
			// 网络错误或其他异常
			if (error instanceof Error) {
				throw error
			} else {
				throw new Error('网络连接失败，请检查网络设置')
			}
		}
	}

	/**
	 * 验证token有效性
	 */
	static async verify(): Promise<VerifyResponse> {
		const token = this.getToken()
		if (!token) {
			throw new Error('未找到认证token')
		}

		const response = await fetch('/admin/verify', {
			headers: {
				'Authorization': `Bearer ${token}`,
			},
		})

		if (!response.ok) {
			// token无效，清除本地存储
			this.logout()
			const error = await response.json()
			throw new Error(error.message || 'token验证失败')
		}

		return response.json()
	}

	/**
	 * 退出登录
	 */
	static logout(): void {
		localStorage.removeItem(this.TOKEN_KEY)
		localStorage.removeItem(this.USER_KEY)
	}

	/**
	 * 获取保存的token
	 */
	static getToken(): string | null {
		return localStorage.getItem(this.TOKEN_KEY)
	}

	/**
	 * 获取保存的用户信息
	 */
	static getUser(): { id: number; username: string } | null {
		const userStr = localStorage.getItem(this.USER_KEY)
		if (!userStr) return null
		
		try {
			return JSON.parse(userStr)
		} catch {
			return null
		}
	}

	/**
	 * 检查是否已登录
	 */
	static isLoggedIn(): boolean {
		return !!this.getToken()
	}

	/**
	 * 获取授权头
	 */
	static getAuthHeaders(): Record<string, string> {
		const token = this.getToken()
		return token ? { Authorization: `Bearer ${token}` } : {}
	}
}

export default AuthService 