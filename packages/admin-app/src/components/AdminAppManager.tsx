import React, { useState } from 'react'
import {
	Table,
	Button,
	Modal,
	Form,
	Input,
	InputNumber,
	message,
	Popconfirm,
	Tag,
	Space,
	Drawer,
	Select,
	Tooltip,
	App as AntApp
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import appService, { DifyApi, IDifyAppItem as ServiceIDifyAppItem } from '../services/app'
import AuthService from '../services/auth'

// 从前台代码中复制的枚举和常量
enum AppModeEnums {
	TEXT_GENERATOR = 'completion',
	CHATBOT = 'chat',
	WORKFLOW = 'workflow',
	CHATFLOW = 'advanced-chat',
	AGENT = 'agent-chat',
}

const AppModeLabels = {
	[AppModeEnums.TEXT_GENERATOR]: 'Text Generator',
	[AppModeEnums.CHATBOT]: 'Chatbot',
	[AppModeEnums.WORKFLOW]: 'Workflow',
	[AppModeEnums.CHATFLOW]: 'Chatflow',
	[AppModeEnums.AGENT]: 'Agent',
}

const AppModeNames = {
	[AppModeEnums.TEXT_GENERATOR]: '文本生成',
	[AppModeEnums.CHATBOT]: '聊天助手',
	[AppModeEnums.WORKFLOW]: '工作流',
	[AppModeEnums.CHATFLOW]: '支持工作流编排的聊天助手',
	[AppModeEnums.AGENT]: '具备推理和自主调用能力的聊天助手',
}

const getAppModelFullName = (mode: AppModeEnums) => {
	return `${AppModeLabels[mode]}（${AppModeNames[mode]}）`
}

const AppModeOptions = [
	AppModeEnums.CHATBOT,
	AppModeEnums.WORKFLOW,
	AppModeEnums.CHATFLOW,
	AppModeEnums.AGENT,
	AppModeEnums.TEXT_GENERATOR,
].map(mode => {
	return {
		label: getAppModelFullName(mode),
		value: mode,
	}
})

const OpeningStatementDisplayModeOptions = [
	{
		label: '默认（开始对话前展示）',
		value: 'default',
	},
	{
		label: '总是展示',
		value: 'always',
	},
]

enum AppDetailDrawerModeEnum {
	create = 'create',
	edit = 'edit',
}

interface IDifyAppItem {
  id: string
  info: {
    name: string
    description: string
    mode: string
    tags: string[]
    points_cost: number
  }
  requestConfig: {
    apiBase: string
    apiKey: string
  }
  answerForm?: {
    enabled: boolean
    feedbackText: string
  }
  inputParams?: {
    enableUpdateAfterCvstStarts: boolean
  }
  extConfig?: {
    conversation?: {
      openingStatement?: {
        displayMode: string
      }
    }
  }
}

// 设置表单组件 - 完全从前台迁移
interface ISettingFormProps {
	formInstance: any
	mode: AppDetailDrawerModeEnum
	appItem?: IDifyAppItem
}

function SettingForm(props: ISettingFormProps) {
	const { formInstance, mode, appItem } = props

	const answerFormEnabled = Form.useWatch('answerForm.enabled', formInstance)

	return (
		<Form
			autoComplete="off"
			form={formInstance}
			labelAlign="left"
			labelCol={{
				span: 5,
			}}
			initialValues={{
				'answerForm.enabled': false,
				'inputParams.enableUpdateAfterCvstStarts': false,
				'extConfig.conversation.openingStatement.displayMode': 'default',
			}}
		>
			      <div className="text-base mb-4 flex items-center">
        <div className="h-4 w-1 bg-blue-500 rounded"></div>
        <div className="ml-2 font-semibold text-gray-800">请求配置</div>
      </div>
			<Form.Item
				label="API Base"
				name="apiBase"
				rules={[{ required: true, message: 'API Base 不能为空' }]}
				tooltip="Dify API 的域名+版本号前缀，如 https://api.dify.ai/v1"
				required
			>
				<Input
					autoComplete="new-password"
					placeholder="请输入 API BASE"
				/>
			</Form.Item>
			<Form.Item
				label="API Secret"
				name="apiKey"
				tooltip="Dify App 的 API Secret (以 app- 开头)"
				rules={[{ required: true, message: 'API Secret 不能为空' }]}
				required
			>
				<Input.Password
					autoComplete="new-password"
					placeholder="请输入 API Secret"
				/>
			</Form.Item>

			      <div className="text-base mb-4 flex items-center">
        <div className="h-4 w-1 bg-blue-500 rounded"></div>
        <div className="ml-2 font-semibold text-gray-800">基本信息</div>
      </div>
			<Form.Item
				name="info.name"
				label="应用名称"
				hidden={mode === AppDetailDrawerModeEnum.create}
			>
				<Input
					disabled
					placeholder="请输入应用名称"
				/>
			</Form.Item>
			<Form.Item
				name="info.mode"
				label="应用类型"
				tooltip="小于或等于 v1.3.1 的 Dify API 不会返回应用类型字段，需要用户自行选择"
				required
				rules={[{ required: true, message: '应用类型不能为空' }]}
			>
				<Select
					placeholder="请选择应用类型"
					options={AppModeOptions}
				/>
			</Form.Item>
			<Form.Item
				name="info.description"
				label="应用描述"
				hidden={mode === AppDetailDrawerModeEnum.create}
			>
				<Input
					disabled
					placeholder="请输入应用描述"
				/>
			</Form.Item>
			<Form.Item
				name="info.tags"
				label="应用标签"
				tooltip="给应用打上标签，便于分类和搜索"
			>
				<Select
					mode="tags"
					placeholder="输入并按回车键添加标签"
				/>
			</Form.Item>
			<Form.Item
				name="info.points_cost"
				label="对话消耗积分"
				tooltip="每次与应用进行对话需要消耗的积分，0为不消耗"
				initialValue={0}
				rules={[{ required: true, message: '请输入对话消耗积分' }]}
				required
			>
				<InputNumber
					min={0}
					placeholder="请输入"
					className="w-full"
				/>
			</Form.Item>

			      <div className="text-base mb-4 flex items-center mt-6">
        <div className="h-4 w-1 bg-blue-500 rounded"></div>
        <div className="ml-2 font-semibold text-gray-800">对话配置</div>
      </div>

			<Form.Item
				label="更新历史参数"
				name="inputParams.enableUpdateAfterCvstStarts"
				tooltip="是否允许更新历史对话的输入参数"
				rules={[{ required: true }]}
				required
			>
				<Select
					placeholder="请选择"
					options={[
						{
							label: '启用',
							value: true,
						},
						{
							label: '禁用',
							value: false,
						},
					]}
				/>
			</Form.Item>

			<Form.Item
				label="开场白展示场景"
				name="extConfig.conversation.openingStatement.displayMode"
				tooltip="配置开场白的展示逻辑"
				rules={[{ required: true }]}
				required
			>
				<Select
					placeholder="请选择"
					options={OpeningStatementDisplayModeOptions}
				/>
			</Form.Item>

			      <div className="text-base mb-4 flex items-center mt-6">
        <div className="h-4 w-1 bg-blue-500 rounded"></div>
        <div className="ml-2 font-semibold text-gray-800">更多配置</div>
      </div>

			<Form.Item
				label="表单回复"
				name="answerForm.enabled"
				tooltip="当工作流需要回复表单给用户填写时，建议开启此功能"
				rules={[{ required: true }]}
				required
			>
				<Select
					placeholder="请选择"
					options={[
						{
							label: '启用',
							value: true,
						},
						{
							label: '禁用',
							value: false,
						},
					]}
				/>
			</Form.Item>
			{answerFormEnabled ? (
				<Form.Item
					label="提交消息文本"
					name="answerForm.feedbackText"
					tooltip="当启用表单回复时，用户填写表单并提交后，默认会以用户角色将填写的表单数据作为消息文本发送，如果配置了此字段，将会固定展示配置的字段值"
				>
					<Input placeholder="请输入提交消息文本" />
				</Form.Item>
			) : null}
		</Form>
	)
}

// 应用编辑抽屉组件 - 完全从前台迁移
interface IAppEditDrawerProps {
	detailDrawerMode: AppDetailDrawerModeEnum
	confirmLoading?: boolean
	appItem?: IDifyAppItem
	open: boolean
	onClose?: () => void
	confirmCallback?: () => void
}

function AppEditDrawer(props: IAppEditDrawerProps) {
	const { message: antMessage } = AntApp.useApp()
	const { detailDrawerMode, appItem, open, onClose, confirmCallback } = props
	const [settingForm] = Form.useForm()
	const [confirmLoading, setConfirmBtnLoading] = useState(false)

	// 当 open 状态变化时重置或设置表单值
	React.useEffect(() => {
		if (!open) {
			settingForm.resetFields()
		} else if (detailDrawerMode === AppDetailDrawerModeEnum.edit && appItem) {
			settingForm.setFieldsValue({
				apiBase: appItem?.requestConfig.apiBase,
				apiKey: appItem?.requestConfig.apiKey,
				'info.name': appItem?.info.name,
				'info.description': appItem?.info.description,
				'info.mode': appItem?.info.mode || AppModeEnums.CHATBOT,
				'answerForm.enabled': appItem?.answerForm?.enabled || false,
				'answerForm.feedbackText': appItem?.answerForm?.feedbackText || '',
				'inputParams.enableUpdateAfterCvstStarts':
					appItem?.inputParams?.enableUpdateAfterCvstStarts || false,
				'extConfig.conversation.openingStatement.displayMode':
					appItem?.extConfig?.conversation?.openingStatement?.displayMode || 'default',
				'info.tags': appItem?.info.tags || [],
				'info.points_cost': appItem?.info.points_cost || 0,
			})
		} else if (detailDrawerMode === AppDetailDrawerModeEnum.create) {
			settingForm.setFieldsValue({
				'info.mode': AppModeEnums.CHATBOT,
				'answerForm.enabled': false,
				'inputParams.enableUpdateAfterCvstStarts': false,
				'extConfig.conversation.openingStatement.displayMode': 'default',
				'info.tags': [],
				'info.points_cost': 0,
			})
		}
	}, [open, detailDrawerMode, appItem])

	const handleSubmit = async () => {
		try {
			await settingForm.validateFields()
			setConfirmBtnLoading(true)

			const values = settingForm.getFieldsValue()

			// 获取 Dify 应用信息
			const newDifyApiInstance = new DifyApi({
				user: { sub: 'admin' }, // 管理员用户
				apiBase: values.apiBase,
				apiKey: values.apiKey,
			})

			const difyAppInfo = await newDifyApiInstance.getAppInfo()
			
			const commonInfo: Omit<IDifyAppItem, 'id'> = {
				info: {
					...difyAppInfo,
					// 兼容处理，当 Dify API 返回的应用信息中没有 mode 时，使用表单中的 mode
					mode: difyAppInfo.mode || values['info.mode'],
					// 显式地从表单中获取 tags 和 points_cost
					tags: values['info.tags'] || [],
					points_cost: values['info.points_cost'] || 0,
				},
				requestConfig: {
					apiBase: values.apiBase,
					apiKey: values.apiKey,
				},
				answerForm: {
					enabled: values['answerForm.enabled'],
					feedbackText: values['answerForm.feedbackText'],
				},
				inputParams: {
					enableUpdateAfterCvstStarts: values['inputParams.enableUpdateAfterCvstStarts'],
				},
				extConfig: {
					conversation: {
						openingStatement: {
							displayMode: values['extConfig.conversation.openingStatement.displayMode'],
						},
					},
				},
			}

			if (detailDrawerMode === AppDetailDrawerModeEnum.edit && appItem) {
				await appService.updateApp(appItem.id, {
					id: appItem.id,
					...commonInfo,
				})
				antMessage.success('编辑应用配置成功')
			} else {
				await appService.createApp({
					id: Math.random().toString(),
					...commonInfo,
				})
				antMessage.success('新增应用配置成功')
			}

			onClose?.()
			confirmCallback?.()
		} catch (error: any) {
			console.error('保存应用配置失败', error)
			antMessage.error(`保存应用配置失败: ${error.message || error}`)
		} finally {
			setConfirmBtnLoading(false)
		}
	}

	return (
		<Drawer
			width={700}
			title={`${detailDrawerMode === AppDetailDrawerModeEnum.create ? '新增应用配置' : `编辑应用配置 - ${appItem?.info.name}`}`}
			open={open}
			onClose={onClose}
			extra={
				<Space>
					<Button onClick={onClose}>取消</Button>
					<Button
						type="primary"
						loading={confirmLoading}
						onClick={handleSubmit}
					>
						{detailDrawerMode === AppDetailDrawerModeEnum.create ? '确定' : '更新'}
					</Button>
				</Space>
			}
		>
			<SettingForm
				formInstance={settingForm}
				mode={detailDrawerMode}
				appItem={appItem}
			/>
		</Drawer>
	)
}

// 主组件
const AdminAppManager: React.FC = () => {
	const { message: antMessage } = AntApp.useApp()
	const [apps, setApps] = useState<IDifyAppItem[]>([])
	const [loading, setLoading] = useState(false)
	const [drawerVisible, setDrawerVisible] = useState(false)
	const [drawerMode, setDrawerMode] = useState<AppDetailDrawerModeEnum>(AppDetailDrawerModeEnum.create)
	const [editingApp, setEditingApp] = useState<IDifyAppItem | undefined>()

	  const fetchApps = async () => {
    setLoading(true)
    try {
      // 确保用户已登录
      if (!AuthService.isLoggedIn()) {
        antMessage.error('请先登录')
        window.location.href = '/admin'
        return
      }

      const data = await appService.getApps()
      setApps(data)
    } catch (error: any) {
      console.error('获取应用列表失败:', error)
      antMessage.error(`获取应用列表失败: ${error.message || error}`)

      // 如果是认证错误，跳转到登录页
      if (error.message?.includes('token') || error.message?.includes('认证')) {
        AuthService.logout()
        window.location.href = '/admin'
      }
    } finally {
      setLoading(false)
    }
  }

	React.useEffect(() => {
		fetchApps()
	}, [])

	const handleAdd = () => {
		setDrawerMode(AppDetailDrawerModeEnum.create)
		setEditingApp(undefined)
		setDrawerVisible(true)
	}

	const handleEdit = (app: IDifyAppItem) => {
		setDrawerMode(AppDetailDrawerModeEnum.edit)
		setEditingApp(app)
		setDrawerVisible(true)
	}

	const handleDelete = async (id: string) => {
		try {
			await appService.deleteApp(id)
			message.success('删除应用成功')
			fetchApps()
		} catch (error) {
			message.error('删除应用失败')
		}
	}

	const handleDrawerClose = () => {
		setDrawerVisible(false)
		setEditingApp(undefined)
	}

	const handleConfirmCallback = () => {
		fetchApps()
	}

	  const columns = [
    {
      title: '应用信息',
      key: 'info',
      width: 250,
      render: (_: any, record: IDifyAppItem) => (
        <div>
          <div className="font-bold">{record.info.name || '未知应用'}</div>
          <div className="text-sm text-gray-500 truncate" style={{ maxWidth: '220px' }}>
            {record.info.description || '暂无描述'}
          </div>
          <div className="mt-2">
            {record.info.tags && record.info.tags.map(tag => (
              <Tag key={tag} color="blue">{tag}</Tag>
            ))}
          </div>
        </div>
      ),
    },
    {
      title: '应用类型',
      dataIndex: ['info', 'mode'],
      key: 'mode',
      width: 120,
      render: (mode: string) => (
        <Tag color="blue" className="px-2 py-1">
          {AppModeNames[mode as AppModeEnums] || mode}
        </Tag>
      ),
    },
    {
      title: 'API 配置',
      dataIndex: ['requestConfig', 'apiBase'],
      key: 'apiBase',
      width: 200,
      ellipsis: true,
      render: (text: string, record: IDifyAppItem) => (
        <div>
          <Tooltip title={text}>
            <div className="text-sm font-mono text-gray-600 truncate">{text}</div>
          </Tooltip>
          <div className="text-xs text-gray-400 mt-1">
            Key: {record.requestConfig?.apiKey?.substring(0, 8) || 'N/A'}...
          </div>
        </div>
      ),
    },
    {
      title: '应用标签',
      dataIndex: ['info', 'tags'],
      key: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <div className="flex flex-wrap gap-1">
          {tags?.length ? tags.slice(0, 2).map(tag => (
            <Tag key={tag} className="text-xs">{tag}</Tag>
          )) : (
            <span className="text-gray-400 text-xs">无标签</span>
          )}
          {tags?.length > 2 && (
            <Tag className="text-xs">+{tags.length - 2}</Tag>
          )}
        </div>
      ),
    },
    {
      title: '消耗积分',
      dataIndex: ['info', 'points_cost'],
      key: 'points_cost',
      width: 100,
      render: (points_cost: number) => points_cost || 0,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: IDifyAppItem) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className="text-blue-600 hover:text-blue-800"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此应用吗？"
            description="删除后将无法恢复"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              className="text-red-500 hover:text-red-700"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

	  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-1">应用配置管理</h2>
            <p className="text-sm text-gray-500">管理和配置所有 Dify 应用</p>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            size="large"
          >
            新增应用配置
          </Button>
        </div>
      </div>

      <div className="p-6">
        <Table
          columns={columns}
          dataSource={apps}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          className="shadow-sm"
        />
      </div>

			<AppEditDrawer
				open={drawerVisible}
				detailDrawerMode={drawerMode}
				appItem={editingApp}
				onClose={handleDrawerClose}
				confirmCallback={handleConfirmCallback}
			/>
		</div>
	)
}

export default AdminAppManager 