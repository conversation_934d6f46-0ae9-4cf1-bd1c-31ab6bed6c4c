import React, { useState } from 'react'
import { Form, Input, Button, Card, Typography, message, Space } from 'antd'
import { UserOutlined, LockOutlined, SafetyCertificateOutlined } from '@ant-design/icons'
import AuthService from '../services/auth'

const { Title, Text } = Typography

interface LoginForm {
	username: string
	password: string
}

interface LoginPageProps {
	onLoginSuccess: () => void
}

const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess }) => {
	const [loading, setLoading] = useState(false)
	const [form] = Form.useForm<LoginForm>()

	const handleLogin = async (values: LoginForm) => {
		setLoading(true)
		try {
			console.log('尝试登录:', values.username)
			await AuthService.login(values)
			message.success('登录成功！')
			onLoginSuccess()
		} catch (error) {
			console.error('登录失败:', error)
			const errorMessage = (error as Error).message || '登录失败'
			console.log('显示错误信息:', errorMessage)
			message.error(errorMessage)
		} finally {
			setLoading(false)
		}
	}

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
			<Card 
				className="w-full max-w-md shadow-2xl border-0"
				bordered={false}
			>
				<div className="text-center mb-8">
					<SafetyCertificateOutlined className="text-4xl text-blue-600 mb-4" />
					<Title level={3} className="mb-2">
						Autobot 管理后台
					</Title>
					<Text type="secondary">
						安全的应用配置管理平台
					</Text>
				</div>

				<Form
					form={form}
					size="large"
					onFinish={handleLogin}
					layout="vertical"
					requiredMark={false}
				>
					<Form.Item
						name="username"
						label="管理员账号"
						rules={[
							{ required: true, message: '请输入管理员账号' },
							{ min: 3, message: '账号长度不能少于3位' }
						]}
					>
						<Input
							prefix={<UserOutlined className="text-gray-400" />}
							placeholder="请输入管理员账号"
						/>
					</Form.Item>

					<Form.Item
						name="password"
						label="登录密码"
						rules={[
							{ required: true, message: '请输入登录密码' },
							{ min: 6, message: '密码长度不能少于6位' }
						]}
					>
						<Input.Password
							prefix={<LockOutlined className="text-gray-400" />}
							placeholder="请输入登录密码"
						/>
					</Form.Item>

					<Form.Item>
						<Button
							type="primary"
							htmlType="submit"
							loading={loading}
							block
							className="h-12 text-lg"
						>
							登录管理后台
						</Button>
					</Form.Item>
				</Form>

				<div className="text-center mt-6">
					<Space direction="vertical" size="small">
						<Text type="secondary" className="text-sm">
							🔐 默认管理员账号
						</Text>
						<Text type="secondary" className="text-xs">
							账号：admin / 密码：admin123
						</Text>
						<Text type="secondary" className="text-xs">
							（生产环境请及时修改默认密码）
						</Text>
					</Space>
				</div>
			</Card>
		</div>
	)
}

export default LoginPage 