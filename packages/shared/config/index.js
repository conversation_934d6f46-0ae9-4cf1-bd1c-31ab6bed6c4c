// 统一配置管理模块
// 避免硬编码，充分利用环境变量

const path = require('path');
const fs = require('fs');

// 尝试加载 dotenv，如果不存在则跳过
try {
  require('dotenv').config();
} catch (error) {
  console.warn('⚠️  dotenv 模块未找到，将使用系统环境变量');
}

// 配置验证函数
function validateConfig() {
  const required = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME', 'JWT_SECRET'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.warn(`⚠️  缺少环境变量: ${missing.join(', ')}`);
    console.warn('请检查 .env 文件或设置相应的环境变量');
  }
}

// 统一配置对象
const config = {
  // 环境信息
  env: process.env.NODE_ENV || 'development',
  isDev: process.env.NODE_ENV === 'development',
  isProd: process.env.NODE_ENV === 'production',
  
  // 项目路径 - 避免硬编码，从环境变量读取
  projectRoot: process.env.PROJECT_ROOT || process.cwd(),
  
  // 服务端口配置
  ports: {
    server: parseInt(process.env.SERVER_PORT) || parseInt(process.env.PORT) || 3010,
    frontend: parseInt(process.env.FRONTEND_PORT) || 5200,
    admin: parseInt(process.env.ADMIN_PORT) || 5202
  },

  // 向后兼容的端口配置
  PORT: parseInt(process.env.SERVER_PORT) || parseInt(process.env.PORT) || 3010,
  
  // 数据库配置 - 完全从环境变量读取
  database: {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 20,
    queueLimit: parseInt(process.env.DB_QUEUE_LIMIT) || 0,
    connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT) || 60000,
    timezone: '+00:00',
    charset: 'utf8mb4',
    multipleStatements: false
  },
  
  // API配置 - 从环境变量读取，避免硬编码
  api: {
    difyBaseUrl: process.env.DIFY_BASE_URL || 'https://api.dify.ai/v1',
    difyApiKey: process.env.DIFY_API_KEY,
    localApiBase: process.env.LOCAL_API_BASE || 'http://127.0.0.1:5001/v1',
    version: process.env.API_VERSION || 'v1',
    prefix: process.env.API_PREFIX || '/api'
  },
  
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },
  
  // 默认管理员配置
  defaultAdmin: {
    username: process.env.DEFAULT_ADMIN_USERNAME || 'admin',
    password: process.env.DEFAULT_ADMIN_PASSWORD || 'admin123',
    email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>'
  },
  
  // 路径配置 - 基于项目根路径动态计算，避免硬编码
  paths: {
    server: path.join(process.env.PROJECT_ROOT || process.cwd(), 'packages/server'),
    frontend: path.join(process.env.PROJECT_ROOT || process.cwd(), 'packages/react-app'),
    admin: path.join(process.env.PROJECT_ROOT || process.cwd(), 'packages/admin-app'),
    uploads: path.join(process.env.PROJECT_ROOT || process.cwd(), process.env.UPLOAD_DIR || 'uploads'),
    logs: path.join(process.env.PROJECT_ROOT || process.cwd(), process.env.LOG_FILE_PATH || 'logs'),
    icons: path.join(process.env.PROJECT_ROOT || process.cwd(), 'packages/server/uploads/icons')
  },
  
  // CORS配置 - 支持服务器部署
  cors: {
    origins: process.env.CORS_ORIGINS?.split(',') || [
      `http://${process.env.SERVER_HOST || 'localhost'}:3000`,
      `http://${process.env.SERVER_HOST || 'localhost'}:${process.env.FRONTEND_PORT || 5200}`,
      `http://${process.env.SERVER_HOST || 'localhost'}:${process.env.ADMIN_PORT || 5202}`,
      // 添加常见的生产环境域名支持
      'http://localhost:3000',
      'http://localhost:5200',
      'http://localhost:5202'
    ]
  },
  
  // 文件上传配置
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10485760, // 10MB
    uploadDir: process.env.UPLOAD_DIR || './uploads'
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    filePath: process.env.LOG_FILE_PATH || './logs'
  },
  
  // Redis缓存配置
  redis: {
    host: process.env.REDIS_HOST || (process.env.NODE_ENV === 'production' ? '127.0.0.1' : null),
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || null,
    db: parseInt(process.env.REDIS_DB) || 0,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'autobot:',
    maxRetriesPerRequest: 3,
    retryDelayOnFailover: 100,
    enableOfflineQueue: false
  },
  
  // 缓存配置
  cache: {
    appListTTL: parseInt(process.env.CACHE_APP_LIST_TTL) || 300, // 5分钟
    appDetailTTL: parseInt(process.env.CACHE_APP_DETAIL_TTL) || 600, // 10分钟
    healthCheckTTL: parseInt(process.env.CACHE_HEALTH_TTL) || 30, // 30秒
    systemSettingsTTL: parseInt(process.env.CACHE_SYSTEM_SETTINGS_TTL) || 300 // 5分钟
  },
  
  // 监控配置
  monitoring: {
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 30000
  },
  
  // 任务调度配置
  scheduler: {
    backupCron: process.env.BACKUP_CRON || '0 2 * * *', // 每天凌晨2点
    logCleanupCron: process.env.LOG_CLEANUP_CRON || '0 3 * * 0', // 每周日凌晨3点
    statsCron: process.env.STATS_CRON || '*/5 * * * *' // 每5分钟
  },
  
  // 安全配置
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    sessionSecret: process.env.SESSION_SECRET,
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 60000,
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    rateLimitLoginMax: parseInt(process.env.RATE_LIMIT_LOGIN_MAX) || 5
  },
  
  // 生产环境检查函数
  isProduction: () => config.env === 'production',
  isDevelopment: () => config.env === 'development',
  
  // 获取完整的上传路径
  getUploadPath: (subDir = '') => {
    return path.join(config.paths.uploads, subDir);
  },
  
  // 获取完整的日志路径
  getLogPath: (filename = '') => {
    return path.join(config.paths.logs, filename);
  }
};

// 验证配置
validateConfig();

// 确保必要的目录存在
[config.paths.uploads, config.paths.logs, config.paths.icons].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

module.exports = config;
